// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: events.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export enum SlotStatus {
  /** PROCESSED - Most common first */
  PROCESSED = 0,
  CONFIRMED = 1,
  ROOTED = 2,
  UNRECOGNIZED = -1,
}

export function slotStatusFromJSON(object: any): SlotStatus {
  switch (object) {
    case 0:
    case "PROCESSED":
      return SlotStatus.PROCESSED;
    case 1:
    case "CONFIRMED":
      return SlotStatus.CONFIRMED;
    case 2:
    case "ROOTED":
      return SlotStatus.ROOTED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SlotStatus.UNRECOGNIZED;
  }
}

export function slotStatusToJSON(object: SlotStatus): string {
  switch (object) {
    case SlotStatus.PROCESSED:
      return "PROCESSED";
    case SlotStatus.CONFIRMED:
      return "CONFIRMED";
    case SlotStatus.ROOTED:
      return "ROOTED";
    case SlotStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum StreamType {
  STREAM_TYPE_UNSPECIFIED = 0,
  STREAM_TYPE_FILTERED = 1,
  STREAM_TYPE_WALLET = 2,
  UNRECOGNIZED = -1,
}

export function streamTypeFromJSON(object: any): StreamType {
  switch (object) {
    case 0:
    case "STREAM_TYPE_UNSPECIFIED":
      return StreamType.STREAM_TYPE_UNSPECIFIED;
    case 1:
    case "STREAM_TYPE_FILTERED":
      return StreamType.STREAM_TYPE_FILTERED;
    case 2:
    case "STREAM_TYPE_WALLET":
      return StreamType.STREAM_TYPE_WALLET;
    case -1:
    case "UNRECOGNIZED":
    default:
      return StreamType.UNRECOGNIZED;
  }
}

export function streamTypeToJSON(object: StreamType): string {
  switch (object) {
    case StreamType.STREAM_TYPE_UNSPECIFIED:
      return "STREAM_TYPE_UNSPECIFIED";
    case StreamType.STREAM_TYPE_FILTERED:
      return "STREAM_TYPE_FILTERED";
    case StreamType.STREAM_TYPE_WALLET:
      return "STREAM_TYPE_WALLET";
    case StreamType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface UpdateAccountEvent {
  /** Fixed length fields first */
  slot: bigint;
  /** 8 bytes, frequently accessed */
  lamports: bigint;
  /** 8 bytes */
  rentEpoch: bigint;
  /** 8 bytes */
  writeVersion: bigint;
  /** 1 byte */
  executable: boolean;
  /** Variable length fields after */
  pubkey: Buffer;
  /** 32 bytes fixed but proto variable */
  owner: Buffer;
  /** variable length */
  data: Buffer;
  /** optional, 64 bytes when present */
  txnSignature?: Buffer | undefined;
}

export interface SlotStatusEvent {
  /** Fixed length fields grouped */
  slot: bigint;
  /** 8 bytes */
  parent: bigint;
  /** enum (4 bytes) */
  status: SlotStatus;
}

export interface MessageHeader {
  /** Group small fixed-size fields together */
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

export interface CompiledInstruction {
  programIdIndex: number;
  /** Variable length after fixed */
  data: Buffer;
  /** Use packed for better encoding */
  accounts: number[];
}

export interface LoadedAddresses {
  writable: Buffer[];
  readonly: Buffer[];
}

export interface MessageAddressTableLookup {
  /** 32 bytes */
  accountKey: Buffer;
  /** 32 bytes */
  writableIndexes: Buffer;
  /** 32 bytes */
  readonlyIndexes: Buffer;
}

/** Unified Message structure that handles both Legacy and v0 */
export interface Message {
  /** 0 for legacy, 1 for v0 */
  version: number;
  header:
    | MessageHeader
    | undefined;
  /** 32 bytes */
  recentBlockHash: Buffer;
  /** Array of 32 byte keys */
  accountKeys: Buffer[];
  instructions: CompiledInstruction[];
  /** Only used for v0 */
  addressTableLookups: MessageAddressTableLookup[];
  /** Only used for v0 */
  loadedAddresses:
    | LoadedAddresses
    | undefined;
  /** Account write permissions */
  isWritable: boolean[];
}

export interface SanitizedTransaction {
  message:
    | Message
    | undefined;
  /** 32 bytes */
  messageHash: Buffer;
  /** Array of 64 byte signatures */
  signatures: Buffer[];
  isSimpleVoteTransaction: boolean;
}

export interface TransactionEvent {
  slot: bigint;
  /** 64 bytes */
  signature: Buffer;
  index: bigint;
  isVote: boolean;
  transaction: SanitizedTransaction | undefined;
  transactionStatusMeta: TransactionStatusMeta | undefined;
}

export interface InnerInstruction {
  instruction: CompiledInstruction | undefined;
  stackHeight?: number | undefined;
}

export interface InnerInstructions {
  index: number;
  instructions: InnerInstruction[];
}

export interface UiTokenAmount {
  uiAmount: number;
  decimals: number;
  amount: string;
  uiAmountString: string;
}

export interface TransactionTokenBalance {
  accountIndex: number;
  mint: string;
  uiTokenAmount: UiTokenAmount | undefined;
  owner: string;
}

export interface Reward {
  pubkey: string;
  lamports: bigint;
  postBalance: bigint;
  rewardType: number;
  commission: number;
}

export interface TransactionStatusMeta {
  /** Most frequently accessed fields first */
  isStatusErr: boolean;
  fee: bigint;
  preBalances: bigint[];
  postBalances: bigint[];
  /** Larger and optional fields after */
  innerInstructions: InnerInstructions[];
  logMessages: string[];
  preTokenBalances: TransactionTokenBalance[];
  postTokenBalances: TransactionTokenBalance[];
  rewards: Reward[];
  errorInfo: string;
}

export interface TransactionEventWrapper {
  streamType: StreamType;
  transaction: TransactionEvent | undefined;
}

export interface MessageWrapper {
  account?: UpdateAccountEvent | undefined;
  slot?: SlotStatusEvent | undefined;
  transaction?: TransactionEventWrapper | undefined;
}

function createBaseUpdateAccountEvent(): UpdateAccountEvent {
  return {
    slot: 0n,
    lamports: 0n,
    rentEpoch: 0n,
    writeVersion: 0n,
    executable: false,
    pubkey: Buffer.alloc(0),
    owner: Buffer.alloc(0),
    data: Buffer.alloc(0),
    txnSignature: undefined,
  };
}

export const UpdateAccountEvent: MessageFns<UpdateAccountEvent> = {
  encode(message: UpdateAccountEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.lamports !== 0n) {
      if (BigInt.asUintN(64, message.lamports) !== message.lamports) {
        throw new globalThis.Error("value provided for field message.lamports of type uint64 too large");
      }
      writer.uint32(16).uint64(message.lamports);
    }
    if (message.rentEpoch !== 0n) {
      if (BigInt.asUintN(64, message.rentEpoch) !== message.rentEpoch) {
        throw new globalThis.Error("value provided for field message.rentEpoch of type uint64 too large");
      }
      writer.uint32(24).uint64(message.rentEpoch);
    }
    if (message.writeVersion !== 0n) {
      if (BigInt.asUintN(64, message.writeVersion) !== message.writeVersion) {
        throw new globalThis.Error("value provided for field message.writeVersion of type uint64 too large");
      }
      writer.uint32(32).uint64(message.writeVersion);
    }
    if (message.executable !== false) {
      writer.uint32(40).bool(message.executable);
    }
    if (message.pubkey.length !== 0) {
      writer.uint32(50).bytes(message.pubkey);
    }
    if (message.owner.length !== 0) {
      writer.uint32(58).bytes(message.owner);
    }
    if (message.data.length !== 0) {
      writer.uint32(66).bytes(message.data);
    }
    if (message.txnSignature !== undefined) {
      writer.uint32(74).bytes(message.txnSignature);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateAccountEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateAccountEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lamports = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.rentEpoch = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.writeVersion = reader.uint64() as bigint;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.executable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.pubkey = Buffer.from(reader.bytes());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.owner = Buffer.from(reader.bytes());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.txnSignature = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<UpdateAccountEvent, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<UpdateAccountEvent | UpdateAccountEvent[]>
      | Iterable<UpdateAccountEvent | UpdateAccountEvent[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UpdateAccountEvent.encode(p).finish()];
        }
      } else {
        yield* [UpdateAccountEvent.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, UpdateAccountEvent>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<UpdateAccountEvent> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UpdateAccountEvent.decode(p)];
        }
      } else {
        yield* [UpdateAccountEvent.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): UpdateAccountEvent {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      lamports: isSet(object.lamports) ? BigInt(object.lamports) : 0n,
      rentEpoch: isSet(object.rentEpoch) ? BigInt(object.rentEpoch) : 0n,
      writeVersion: isSet(object.writeVersion) ? BigInt(object.writeVersion) : 0n,
      executable: isSet(object.executable) ? globalThis.Boolean(object.executable) : false,
      pubkey: isSet(object.pubkey) ? Buffer.from(bytesFromBase64(object.pubkey)) : Buffer.alloc(0),
      owner: isSet(object.owner) ? Buffer.from(bytesFromBase64(object.owner)) : Buffer.alloc(0),
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
      txnSignature: isSet(object.txnSignature) ? Buffer.from(bytesFromBase64(object.txnSignature)) : undefined,
    };
  },

  toJSON(message: UpdateAccountEvent): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.lamports !== 0n) {
      obj.lamports = message.lamports.toString();
    }
    if (message.rentEpoch !== 0n) {
      obj.rentEpoch = message.rentEpoch.toString();
    }
    if (message.writeVersion !== 0n) {
      obj.writeVersion = message.writeVersion.toString();
    }
    if (message.executable !== false) {
      obj.executable = message.executable;
    }
    if (message.pubkey.length !== 0) {
      obj.pubkey = base64FromBytes(message.pubkey);
    }
    if (message.owner.length !== 0) {
      obj.owner = base64FromBytes(message.owner);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    if (message.txnSignature !== undefined) {
      obj.txnSignature = base64FromBytes(message.txnSignature);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateAccountEvent>, I>>(base?: I): UpdateAccountEvent {
    return UpdateAccountEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAccountEvent>, I>>(object: I): UpdateAccountEvent {
    const message = createBaseUpdateAccountEvent();
    message.slot = object.slot ?? 0n;
    message.lamports = object.lamports ?? 0n;
    message.rentEpoch = object.rentEpoch ?? 0n;
    message.writeVersion = object.writeVersion ?? 0n;
    message.executable = object.executable ?? false;
    message.pubkey = object.pubkey ?? Buffer.alloc(0);
    message.owner = object.owner ?? Buffer.alloc(0);
    message.data = object.data ?? Buffer.alloc(0);
    message.txnSignature = object.txnSignature ?? undefined;
    return message;
  },
};

function createBaseSlotStatusEvent(): SlotStatusEvent {
  return { slot: 0n, parent: 0n, status: 0 };
}

export const SlotStatusEvent: MessageFns<SlotStatusEvent> = {
  encode(message: SlotStatusEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.parent !== 0n) {
      if (BigInt.asUintN(64, message.parent) !== message.parent) {
        throw new globalThis.Error("value provided for field message.parent of type uint64 too large");
      }
      writer.uint32(16).uint64(message.parent);
    }
    if (message.status !== 0) {
      writer.uint32(24).int32(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SlotStatusEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSlotStatusEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.parent = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SlotStatusEvent, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SlotStatusEvent | SlotStatusEvent[]> | Iterable<SlotStatusEvent | SlotStatusEvent[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SlotStatusEvent.encode(p).finish()];
        }
      } else {
        yield* [SlotStatusEvent.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SlotStatusEvent>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SlotStatusEvent> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SlotStatusEvent.decode(p)];
        }
      } else {
        yield* [SlotStatusEvent.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SlotStatusEvent {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      parent: isSet(object.parent) ? BigInt(object.parent) : 0n,
      status: isSet(object.status) ? slotStatusFromJSON(object.status) : 0,
    };
  },

  toJSON(message: SlotStatusEvent): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.parent !== 0n) {
      obj.parent = message.parent.toString();
    }
    if (message.status !== 0) {
      obj.status = slotStatusToJSON(message.status);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SlotStatusEvent>, I>>(base?: I): SlotStatusEvent {
    return SlotStatusEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SlotStatusEvent>, I>>(object: I): SlotStatusEvent {
    const message = createBaseSlotStatusEvent();
    message.slot = object.slot ?? 0n;
    message.parent = object.parent ?? 0n;
    message.status = object.status ?? 0;
    return message;
  },
};

function createBaseMessageHeader(): MessageHeader {
  return { numRequiredSignatures: 0, numReadonlySignedAccounts: 0, numReadonlyUnsignedAccounts: 0 };
}

export const MessageHeader: MessageFns<MessageHeader> = {
  encode(message: MessageHeader, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.numRequiredSignatures !== 0) {
      writer.uint32(8).uint32(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      writer.uint32(16).uint32(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      writer.uint32(24).uint32(message.numReadonlyUnsignedAccounts);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageHeader {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageHeader();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.numRequiredSignatures = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.numReadonlySignedAccounts = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.numReadonlyUnsignedAccounts = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageHeader, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<MessageHeader | MessageHeader[]> | Iterable<MessageHeader | MessageHeader[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.encode(p).finish()];
        }
      } else {
        yield* [MessageHeader.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageHeader>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageHeader> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.decode(p)];
        }
      } else {
        yield* [MessageHeader.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageHeader {
    return {
      numRequiredSignatures: isSet(object.numRequiredSignatures) ? globalThis.Number(object.numRequiredSignatures) : 0,
      numReadonlySignedAccounts: isSet(object.numReadonlySignedAccounts)
        ? globalThis.Number(object.numReadonlySignedAccounts)
        : 0,
      numReadonlyUnsignedAccounts: isSet(object.numReadonlyUnsignedAccounts)
        ? globalThis.Number(object.numReadonlyUnsignedAccounts)
        : 0,
    };
  },

  toJSON(message: MessageHeader): unknown {
    const obj: any = {};
    if (message.numRequiredSignatures !== 0) {
      obj.numRequiredSignatures = Math.round(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      obj.numReadonlySignedAccounts = Math.round(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      obj.numReadonlyUnsignedAccounts = Math.round(message.numReadonlyUnsignedAccounts);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageHeader>, I>>(base?: I): MessageHeader {
    return MessageHeader.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageHeader>, I>>(object: I): MessageHeader {
    const message = createBaseMessageHeader();
    message.numRequiredSignatures = object.numRequiredSignatures ?? 0;
    message.numReadonlySignedAccounts = object.numReadonlySignedAccounts ?? 0;
    message.numReadonlyUnsignedAccounts = object.numReadonlyUnsignedAccounts ?? 0;
    return message;
  },
};

function createBaseCompiledInstruction(): CompiledInstruction {
  return { programIdIndex: 0, data: Buffer.alloc(0), accounts: [] };
}

export const CompiledInstruction: MessageFns<CompiledInstruction> = {
  encode(message: CompiledInstruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programIdIndex !== 0) {
      writer.uint32(8).uint32(message.programIdIndex);
    }
    if (message.data.length !== 0) {
      writer.uint32(18).bytes(message.data);
    }
    writer.uint32(26).fork();
    for (const v of message.accounts) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompiledInstruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompiledInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.programIdIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.accounts.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.accounts.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<CompiledInstruction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<CompiledInstruction | CompiledInstruction[]>
      | Iterable<CompiledInstruction | CompiledInstruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.encode(p).finish()];
        }
      } else {
        yield* [CompiledInstruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, CompiledInstruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<CompiledInstruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.decode(p)];
        }
      } else {
        yield* [CompiledInstruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): CompiledInstruction {
    return {
      programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
      accounts: globalThis.Array.isArray(object?.accounts) ? object.accounts.map((e: any) => globalThis.Number(e)) : [],
    };
  },

  toJSON(message: CompiledInstruction): unknown {
    const obj: any = {};
    if (message.programIdIndex !== 0) {
      obj.programIdIndex = Math.round(message.programIdIndex);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    if (message.accounts?.length) {
      obj.accounts = message.accounts.map((e) => Math.round(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompiledInstruction>, I>>(base?: I): CompiledInstruction {
    return CompiledInstruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompiledInstruction>, I>>(object: I): CompiledInstruction {
    const message = createBaseCompiledInstruction();
    message.programIdIndex = object.programIdIndex ?? 0;
    message.data = object.data ?? Buffer.alloc(0);
    message.accounts = object.accounts?.map((e) => e) || [];
    return message;
  },
};

function createBaseLoadedAddresses(): LoadedAddresses {
  return { writable: [], readonly: [] };
}

export const LoadedAddresses: MessageFns<LoadedAddresses> = {
  encode(message: LoadedAddresses, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.writable) {
      writer.uint32(10).bytes(v!);
    }
    for (const v of message.readonly) {
      writer.uint32(18).bytes(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LoadedAddresses {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLoadedAddresses();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.writable.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readonly.push(Buffer.from(reader.bytes()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<LoadedAddresses, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<LoadedAddresses | LoadedAddresses[]> | Iterable<LoadedAddresses | LoadedAddresses[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [LoadedAddresses.encode(p).finish()];
        }
      } else {
        yield* [LoadedAddresses.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, LoadedAddresses>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<LoadedAddresses> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [LoadedAddresses.decode(p)];
        }
      } else {
        yield* [LoadedAddresses.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): LoadedAddresses {
    return {
      writable: globalThis.Array.isArray(object?.writable)
        ? object.writable.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      readonly: globalThis.Array.isArray(object?.readonly)
        ? object.readonly.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
    };
  },

  toJSON(message: LoadedAddresses): unknown {
    const obj: any = {};
    if (message.writable?.length) {
      obj.writable = message.writable.map((e) => base64FromBytes(e));
    }
    if (message.readonly?.length) {
      obj.readonly = message.readonly.map((e) => base64FromBytes(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LoadedAddresses>, I>>(base?: I): LoadedAddresses {
    return LoadedAddresses.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoadedAddresses>, I>>(object: I): LoadedAddresses {
    const message = createBaseLoadedAddresses();
    message.writable = object.writable?.map((e) => e) || [];
    message.readonly = object.readonly?.map((e) => e) || [];
    return message;
  },
};

function createBaseMessageAddressTableLookup(): MessageAddressTableLookup {
  return { accountKey: Buffer.alloc(0), writableIndexes: Buffer.alloc(0), readonlyIndexes: Buffer.alloc(0) };
}

export const MessageAddressTableLookup: MessageFns<MessageAddressTableLookup> = {
  encode(message: MessageAddressTableLookup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountKey.length !== 0) {
      writer.uint32(10).bytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      writer.uint32(18).bytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      writer.uint32(26).bytes(message.readonlyIndexes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageAddressTableLookup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageAddressTableLookup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountKey = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writableIndexes = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.readonlyIndexes = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageAddressTableLookup, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<MessageAddressTableLookup | MessageAddressTableLookup[]>
      | Iterable<MessageAddressTableLookup | MessageAddressTableLookup[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.encode(p).finish()];
        }
      } else {
        yield* [MessageAddressTableLookup.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageAddressTableLookup>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageAddressTableLookup> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.decode(p)];
        }
      } else {
        yield* [MessageAddressTableLookup.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageAddressTableLookup {
    return {
      accountKey: isSet(object.accountKey) ? Buffer.from(bytesFromBase64(object.accountKey)) : Buffer.alloc(0),
      writableIndexes: isSet(object.writableIndexes)
        ? Buffer.from(bytesFromBase64(object.writableIndexes))
        : Buffer.alloc(0),
      readonlyIndexes: isSet(object.readonlyIndexes)
        ? Buffer.from(bytesFromBase64(object.readonlyIndexes))
        : Buffer.alloc(0),
    };
  },

  toJSON(message: MessageAddressTableLookup): unknown {
    const obj: any = {};
    if (message.accountKey.length !== 0) {
      obj.accountKey = base64FromBytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      obj.writableIndexes = base64FromBytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      obj.readonlyIndexes = base64FromBytes(message.readonlyIndexes);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(base?: I): MessageAddressTableLookup {
    return MessageAddressTableLookup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(object: I): MessageAddressTableLookup {
    const message = createBaseMessageAddressTableLookup();
    message.accountKey = object.accountKey ?? Buffer.alloc(0);
    message.writableIndexes = object.writableIndexes ?? Buffer.alloc(0);
    message.readonlyIndexes = object.readonlyIndexes ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseMessage(): Message {
  return {
    version: 0,
    header: undefined,
    recentBlockHash: Buffer.alloc(0),
    accountKeys: [],
    instructions: [],
    addressTableLookups: [],
    loadedAddresses: undefined,
    isWritable: [],
  };
}

export const Message: MessageFns<Message> = {
  encode(message: Message, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.version !== 0) {
      writer.uint32(8).uint32(message.version);
    }
    if (message.header !== undefined) {
      MessageHeader.encode(message.header, writer.uint32(18).fork()).join();
    }
    if (message.recentBlockHash.length !== 0) {
      writer.uint32(26).bytes(message.recentBlockHash);
    }
    for (const v of message.accountKeys) {
      writer.uint32(34).bytes(v!);
    }
    for (const v of message.instructions) {
      CompiledInstruction.encode(v!, writer.uint32(42).fork()).join();
    }
    for (const v of message.addressTableLookups) {
      MessageAddressTableLookup.encode(v!, writer.uint32(50).fork()).join();
    }
    if (message.loadedAddresses !== undefined) {
      LoadedAddresses.encode(message.loadedAddresses, writer.uint32(58).fork()).join();
    }
    writer.uint32(66).fork();
    for (const v of message.isWritable) {
      writer.bool(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Message {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.version = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.header = MessageHeader.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.recentBlockHash = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.accountKeys.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.instructions.push(CompiledInstruction.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.addressTableLookups.push(MessageAddressTableLookup.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.loadedAddresses = LoadedAddresses.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag === 64) {
            message.isWritable.push(reader.bool());

            continue;
          }

          if (tag === 66) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.isWritable.push(reader.bool());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Message, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Message | Message[]> | Iterable<Message | Message[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Message.encode(p).finish()];
        }
      } else {
        yield* [Message.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Message>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Message> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Message.decode(p)];
        }
      } else {
        yield* [Message.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Message {
    return {
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      header: isSet(object.header) ? MessageHeader.fromJSON(object.header) : undefined,
      recentBlockHash: isSet(object.recentBlockHash)
        ? Buffer.from(bytesFromBase64(object.recentBlockHash))
        : Buffer.alloc(0),
      accountKeys: globalThis.Array.isArray(object?.accountKeys)
        ? object.accountKeys.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => CompiledInstruction.fromJSON(e))
        : [],
      addressTableLookups: globalThis.Array.isArray(object?.addressTableLookups)
        ? object.addressTableLookups.map((e: any) => MessageAddressTableLookup.fromJSON(e))
        : [],
      loadedAddresses: isSet(object.loadedAddresses) ? LoadedAddresses.fromJSON(object.loadedAddresses) : undefined,
      isWritable: globalThis.Array.isArray(object?.isWritable)
        ? object.isWritable.map((e: any) => globalThis.Boolean(e))
        : [],
    };
  },

  toJSON(message: Message): unknown {
    const obj: any = {};
    if (message.version !== 0) {
      obj.version = Math.round(message.version);
    }
    if (message.header !== undefined) {
      obj.header = MessageHeader.toJSON(message.header);
    }
    if (message.recentBlockHash.length !== 0) {
      obj.recentBlockHash = base64FromBytes(message.recentBlockHash);
    }
    if (message.accountKeys?.length) {
      obj.accountKeys = message.accountKeys.map((e) => base64FromBytes(e));
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => CompiledInstruction.toJSON(e));
    }
    if (message.addressTableLookups?.length) {
      obj.addressTableLookups = message.addressTableLookups.map((e) => MessageAddressTableLookup.toJSON(e));
    }
    if (message.loadedAddresses !== undefined) {
      obj.loadedAddresses = LoadedAddresses.toJSON(message.loadedAddresses);
    }
    if (message.isWritable?.length) {
      obj.isWritable = message.isWritable;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Message>, I>>(base?: I): Message {
    return Message.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Message>, I>>(object: I): Message {
    const message = createBaseMessage();
    message.version = object.version ?? 0;
    message.header = (object.header !== undefined && object.header !== null)
      ? MessageHeader.fromPartial(object.header)
      : undefined;
    message.recentBlockHash = object.recentBlockHash ?? Buffer.alloc(0);
    message.accountKeys = object.accountKeys?.map((e) => e) || [];
    message.instructions = object.instructions?.map((e) => CompiledInstruction.fromPartial(e)) || [];
    message.addressTableLookups = object.addressTableLookups?.map((e) => MessageAddressTableLookup.fromPartial(e)) ||
      [];
    message.loadedAddresses = (object.loadedAddresses !== undefined && object.loadedAddresses !== null)
      ? LoadedAddresses.fromPartial(object.loadedAddresses)
      : undefined;
    message.isWritable = object.isWritable?.map((e) => e) || [];
    return message;
  },
};

function createBaseSanitizedTransaction(): SanitizedTransaction {
  return { message: undefined, messageHash: Buffer.alloc(0), signatures: [], isSimpleVoteTransaction: false };
}

export const SanitizedTransaction: MessageFns<SanitizedTransaction> = {
  encode(message: SanitizedTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.message !== undefined) {
      Message.encode(message.message, writer.uint32(10).fork()).join();
    }
    if (message.messageHash.length !== 0) {
      writer.uint32(18).bytes(message.messageHash);
    }
    for (const v of message.signatures) {
      writer.uint32(26).bytes(v!);
    }
    if (message.isSimpleVoteTransaction !== false) {
      writer.uint32(32).bool(message.isSimpleVoteTransaction);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SanitizedTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSanitizedTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.message = Message.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.messageHash = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.signatures.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isSimpleVoteTransaction = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SanitizedTransaction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SanitizedTransaction | SanitizedTransaction[]>
      | Iterable<SanitizedTransaction | SanitizedTransaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SanitizedTransaction.encode(p).finish()];
        }
      } else {
        yield* [SanitizedTransaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SanitizedTransaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SanitizedTransaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SanitizedTransaction.decode(p)];
        }
      } else {
        yield* [SanitizedTransaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SanitizedTransaction {
    return {
      message: isSet(object.message) ? Message.fromJSON(object.message) : undefined,
      messageHash: isSet(object.messageHash) ? Buffer.from(bytesFromBase64(object.messageHash)) : Buffer.alloc(0),
      signatures: globalThis.Array.isArray(object?.signatures)
        ? object.signatures.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      isSimpleVoteTransaction: isSet(object.isSimpleVoteTransaction)
        ? globalThis.Boolean(object.isSimpleVoteTransaction)
        : false,
    };
  },

  toJSON(message: SanitizedTransaction): unknown {
    const obj: any = {};
    if (message.message !== undefined) {
      obj.message = Message.toJSON(message.message);
    }
    if (message.messageHash.length !== 0) {
      obj.messageHash = base64FromBytes(message.messageHash);
    }
    if (message.signatures?.length) {
      obj.signatures = message.signatures.map((e) => base64FromBytes(e));
    }
    if (message.isSimpleVoteTransaction !== false) {
      obj.isSimpleVoteTransaction = message.isSimpleVoteTransaction;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SanitizedTransaction>, I>>(base?: I): SanitizedTransaction {
    return SanitizedTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SanitizedTransaction>, I>>(object: I): SanitizedTransaction {
    const message = createBaseSanitizedTransaction();
    message.message = (object.message !== undefined && object.message !== null)
      ? Message.fromPartial(object.message)
      : undefined;
    message.messageHash = object.messageHash ?? Buffer.alloc(0);
    message.signatures = object.signatures?.map((e) => e) || [];
    message.isSimpleVoteTransaction = object.isSimpleVoteTransaction ?? false;
    return message;
  },
};

function createBaseTransactionEvent(): TransactionEvent {
  return {
    slot: 0n,
    signature: Buffer.alloc(0),
    index: 0n,
    isVote: false,
    transaction: undefined,
    transactionStatusMeta: undefined,
  };
}

export const TransactionEvent: MessageFns<TransactionEvent> = {
  encode(message: TransactionEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.signature.length !== 0) {
      writer.uint32(18).bytes(message.signature);
    }
    if (message.index !== 0n) {
      if (BigInt.asUintN(64, message.index) !== message.index) {
        throw new globalThis.Error("value provided for field message.index of type uint64 too large");
      }
      writer.uint32(24).uint64(message.index);
    }
    if (message.isVote !== false) {
      writer.uint32(32).bool(message.isVote);
    }
    if (message.transaction !== undefined) {
      SanitizedTransaction.encode(message.transaction, writer.uint32(42).fork()).join();
    }
    if (message.transactionStatusMeta !== undefined) {
      TransactionStatusMeta.encode(message.transactionStatusMeta, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.signature = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.index = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isVote = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.transaction = SanitizedTransaction.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.transactionStatusMeta = TransactionStatusMeta.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TransactionEvent, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<TransactionEvent | TransactionEvent[]> | Iterable<TransactionEvent | TransactionEvent[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionEvent.encode(p).finish()];
        }
      } else {
        yield* [TransactionEvent.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TransactionEvent>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TransactionEvent> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionEvent.decode(p)];
        }
      } else {
        yield* [TransactionEvent.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TransactionEvent {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      signature: isSet(object.signature) ? Buffer.from(bytesFromBase64(object.signature)) : Buffer.alloc(0),
      index: isSet(object.index) ? BigInt(object.index) : 0n,
      isVote: isSet(object.isVote) ? globalThis.Boolean(object.isVote) : false,
      transaction: isSet(object.transaction) ? SanitizedTransaction.fromJSON(object.transaction) : undefined,
      transactionStatusMeta: isSet(object.transactionStatusMeta)
        ? TransactionStatusMeta.fromJSON(object.transactionStatusMeta)
        : undefined,
    };
  },

  toJSON(message: TransactionEvent): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.signature.length !== 0) {
      obj.signature = base64FromBytes(message.signature);
    }
    if (message.index !== 0n) {
      obj.index = message.index.toString();
    }
    if (message.isVote !== false) {
      obj.isVote = message.isVote;
    }
    if (message.transaction !== undefined) {
      obj.transaction = SanitizedTransaction.toJSON(message.transaction);
    }
    if (message.transactionStatusMeta !== undefined) {
      obj.transactionStatusMeta = TransactionStatusMeta.toJSON(message.transactionStatusMeta);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransactionEvent>, I>>(base?: I): TransactionEvent {
    return TransactionEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionEvent>, I>>(object: I): TransactionEvent {
    const message = createBaseTransactionEvent();
    message.slot = object.slot ?? 0n;
    message.signature = object.signature ?? Buffer.alloc(0);
    message.index = object.index ?? 0n;
    message.isVote = object.isVote ?? false;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? SanitizedTransaction.fromPartial(object.transaction)
      : undefined;
    message.transactionStatusMeta =
      (object.transactionStatusMeta !== undefined && object.transactionStatusMeta !== null)
        ? TransactionStatusMeta.fromPartial(object.transactionStatusMeta)
        : undefined;
    return message;
  },
};

function createBaseInnerInstruction(): InnerInstruction {
  return { instruction: undefined, stackHeight: undefined };
}

export const InnerInstruction: MessageFns<InnerInstruction> = {
  encode(message: InnerInstruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.instruction !== undefined) {
      CompiledInstruction.encode(message.instruction, writer.uint32(10).fork()).join();
    }
    if (message.stackHeight !== undefined) {
      writer.uint32(16).uint32(message.stackHeight);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InnerInstruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInnerInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.instruction = CompiledInstruction.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.stackHeight = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<InnerInstruction, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<InnerInstruction | InnerInstruction[]> | Iterable<InnerInstruction | InnerInstruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstruction.encode(p).finish()];
        }
      } else {
        yield* [InnerInstruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, InnerInstruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<InnerInstruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstruction.decode(p)];
        }
      } else {
        yield* [InnerInstruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): InnerInstruction {
    return {
      instruction: isSet(object.instruction) ? CompiledInstruction.fromJSON(object.instruction) : undefined,
      stackHeight: isSet(object.stackHeight) ? globalThis.Number(object.stackHeight) : undefined,
    };
  },

  toJSON(message: InnerInstruction): unknown {
    const obj: any = {};
    if (message.instruction !== undefined) {
      obj.instruction = CompiledInstruction.toJSON(message.instruction);
    }
    if (message.stackHeight !== undefined) {
      obj.stackHeight = Math.round(message.stackHeight);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InnerInstruction>, I>>(base?: I): InnerInstruction {
    return InnerInstruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InnerInstruction>, I>>(object: I): InnerInstruction {
    const message = createBaseInnerInstruction();
    message.instruction = (object.instruction !== undefined && object.instruction !== null)
      ? CompiledInstruction.fromPartial(object.instruction)
      : undefined;
    message.stackHeight = object.stackHeight ?? undefined;
    return message;
  },
};

function createBaseInnerInstructions(): InnerInstructions {
  return { index: 0, instructions: [] };
}

export const InnerInstructions: MessageFns<InnerInstructions> = {
  encode(message: InnerInstructions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.index !== 0) {
      writer.uint32(8).uint32(message.index);
    }
    for (const v of message.instructions) {
      InnerInstruction.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InnerInstructions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInnerInstructions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.index = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.instructions.push(InnerInstruction.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<InnerInstructions, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<InnerInstructions | InnerInstructions[]> | Iterable<InnerInstructions | InnerInstructions[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstructions.encode(p).finish()];
        }
      } else {
        yield* [InnerInstructions.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, InnerInstructions>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<InnerInstructions> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstructions.decode(p)];
        }
      } else {
        yield* [InnerInstructions.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): InnerInstructions {
    return {
      index: isSet(object.index) ? globalThis.Number(object.index) : 0,
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => InnerInstruction.fromJSON(e))
        : [],
    };
  },

  toJSON(message: InnerInstructions): unknown {
    const obj: any = {};
    if (message.index !== 0) {
      obj.index = Math.round(message.index);
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => InnerInstruction.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InnerInstructions>, I>>(base?: I): InnerInstructions {
    return InnerInstructions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InnerInstructions>, I>>(object: I): InnerInstructions {
    const message = createBaseInnerInstructions();
    message.index = object.index ?? 0;
    message.instructions = object.instructions?.map((e) => InnerInstruction.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUiTokenAmount(): UiTokenAmount {
  return { uiAmount: 0, decimals: 0, amount: "", uiAmountString: "" };
}

export const UiTokenAmount: MessageFns<UiTokenAmount> = {
  encode(message: UiTokenAmount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uiAmount !== 0) {
      writer.uint32(9).double(message.uiAmount);
    }
    if (message.decimals !== 0) {
      writer.uint32(16).uint32(message.decimals);
    }
    if (message.amount !== "") {
      writer.uint32(26).string(message.amount);
    }
    if (message.uiAmountString !== "") {
      writer.uint32(34).string(message.uiAmountString);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UiTokenAmount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUiTokenAmount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.uiAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.decimals = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.amount = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.uiAmountString = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<UiTokenAmount, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<UiTokenAmount | UiTokenAmount[]> | Iterable<UiTokenAmount | UiTokenAmount[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UiTokenAmount.encode(p).finish()];
        }
      } else {
        yield* [UiTokenAmount.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, UiTokenAmount>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<UiTokenAmount> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UiTokenAmount.decode(p)];
        }
      } else {
        yield* [UiTokenAmount.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): UiTokenAmount {
    return {
      uiAmount: isSet(object.uiAmount) ? globalThis.Number(object.uiAmount) : 0,
      decimals: isSet(object.decimals) ? globalThis.Number(object.decimals) : 0,
      amount: isSet(object.amount) ? globalThis.String(object.amount) : "",
      uiAmountString: isSet(object.uiAmountString) ? globalThis.String(object.uiAmountString) : "",
    };
  },

  toJSON(message: UiTokenAmount): unknown {
    const obj: any = {};
    if (message.uiAmount !== 0) {
      obj.uiAmount = message.uiAmount;
    }
    if (message.decimals !== 0) {
      obj.decimals = Math.round(message.decimals);
    }
    if (message.amount !== "") {
      obj.amount = message.amount;
    }
    if (message.uiAmountString !== "") {
      obj.uiAmountString = message.uiAmountString;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UiTokenAmount>, I>>(base?: I): UiTokenAmount {
    return UiTokenAmount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UiTokenAmount>, I>>(object: I): UiTokenAmount {
    const message = createBaseUiTokenAmount();
    message.uiAmount = object.uiAmount ?? 0;
    message.decimals = object.decimals ?? 0;
    message.amount = object.amount ?? "";
    message.uiAmountString = object.uiAmountString ?? "";
    return message;
  },
};

function createBaseTransactionTokenBalance(): TransactionTokenBalance {
  return { accountIndex: 0, mint: "", uiTokenAmount: undefined, owner: "" };
}

export const TransactionTokenBalance: MessageFns<TransactionTokenBalance> = {
  encode(message: TransactionTokenBalance, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountIndex !== 0) {
      writer.uint32(8).uint32(message.accountIndex);
    }
    if (message.mint !== "") {
      writer.uint32(18).string(message.mint);
    }
    if (message.uiTokenAmount !== undefined) {
      UiTokenAmount.encode(message.uiTokenAmount, writer.uint32(26).fork()).join();
    }
    if (message.owner !== "") {
      writer.uint32(34).string(message.owner);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionTokenBalance {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionTokenBalance();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.accountIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.mint = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.uiTokenAmount = UiTokenAmount.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.owner = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TransactionTokenBalance, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<TransactionTokenBalance | TransactionTokenBalance[]>
      | Iterable<TransactionTokenBalance | TransactionTokenBalance[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionTokenBalance.encode(p).finish()];
        }
      } else {
        yield* [TransactionTokenBalance.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TransactionTokenBalance>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TransactionTokenBalance> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionTokenBalance.decode(p)];
        }
      } else {
        yield* [TransactionTokenBalance.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TransactionTokenBalance {
    return {
      accountIndex: isSet(object.accountIndex) ? globalThis.Number(object.accountIndex) : 0,
      mint: isSet(object.mint) ? globalThis.String(object.mint) : "",
      uiTokenAmount: isSet(object.uiTokenAmount) ? UiTokenAmount.fromJSON(object.uiTokenAmount) : undefined,
      owner: isSet(object.owner) ? globalThis.String(object.owner) : "",
    };
  },

  toJSON(message: TransactionTokenBalance): unknown {
    const obj: any = {};
    if (message.accountIndex !== 0) {
      obj.accountIndex = Math.round(message.accountIndex);
    }
    if (message.mint !== "") {
      obj.mint = message.mint;
    }
    if (message.uiTokenAmount !== undefined) {
      obj.uiTokenAmount = UiTokenAmount.toJSON(message.uiTokenAmount);
    }
    if (message.owner !== "") {
      obj.owner = message.owner;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransactionTokenBalance>, I>>(base?: I): TransactionTokenBalance {
    return TransactionTokenBalance.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionTokenBalance>, I>>(object: I): TransactionTokenBalance {
    const message = createBaseTransactionTokenBalance();
    message.accountIndex = object.accountIndex ?? 0;
    message.mint = object.mint ?? "";
    message.uiTokenAmount = (object.uiTokenAmount !== undefined && object.uiTokenAmount !== null)
      ? UiTokenAmount.fromPartial(object.uiTokenAmount)
      : undefined;
    message.owner = object.owner ?? "";
    return message;
  },
};

function createBaseReward(): Reward {
  return { pubkey: "", lamports: 0n, postBalance: 0n, rewardType: 0, commission: 0 };
}

export const Reward: MessageFns<Reward> = {
  encode(message: Reward, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pubkey !== "") {
      writer.uint32(10).string(message.pubkey);
    }
    if (message.lamports !== 0n) {
      if (BigInt.asIntN(64, message.lamports) !== message.lamports) {
        throw new globalThis.Error("value provided for field message.lamports of type int64 too large");
      }
      writer.uint32(16).int64(message.lamports);
    }
    if (message.postBalance !== 0n) {
      if (BigInt.asUintN(64, message.postBalance) !== message.postBalance) {
        throw new globalThis.Error("value provided for field message.postBalance of type uint64 too large");
      }
      writer.uint32(24).uint64(message.postBalance);
    }
    if (message.rewardType !== 0) {
      writer.uint32(32).int32(message.rewardType);
    }
    if (message.commission !== 0) {
      writer.uint32(40).uint32(message.commission);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Reward {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReward();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pubkey = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lamports = reader.int64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.postBalance = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.rewardType = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.commission = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Reward, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Reward | Reward[]> | Iterable<Reward | Reward[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Reward.encode(p).finish()];
        }
      } else {
        yield* [Reward.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Reward>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Reward> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Reward.decode(p)];
        }
      } else {
        yield* [Reward.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Reward {
    return {
      pubkey: isSet(object.pubkey) ? globalThis.String(object.pubkey) : "",
      lamports: isSet(object.lamports) ? BigInt(object.lamports) : 0n,
      postBalance: isSet(object.postBalance) ? BigInt(object.postBalance) : 0n,
      rewardType: isSet(object.rewardType) ? globalThis.Number(object.rewardType) : 0,
      commission: isSet(object.commission) ? globalThis.Number(object.commission) : 0,
    };
  },

  toJSON(message: Reward): unknown {
    const obj: any = {};
    if (message.pubkey !== "") {
      obj.pubkey = message.pubkey;
    }
    if (message.lamports !== 0n) {
      obj.lamports = message.lamports.toString();
    }
    if (message.postBalance !== 0n) {
      obj.postBalance = message.postBalance.toString();
    }
    if (message.rewardType !== 0) {
      obj.rewardType = Math.round(message.rewardType);
    }
    if (message.commission !== 0) {
      obj.commission = Math.round(message.commission);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Reward>, I>>(base?: I): Reward {
    return Reward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Reward>, I>>(object: I): Reward {
    const message = createBaseReward();
    message.pubkey = object.pubkey ?? "";
    message.lamports = object.lamports ?? 0n;
    message.postBalance = object.postBalance ?? 0n;
    message.rewardType = object.rewardType ?? 0;
    message.commission = object.commission ?? 0;
    return message;
  },
};

function createBaseTransactionStatusMeta(): TransactionStatusMeta {
  return {
    isStatusErr: false,
    fee: 0n,
    preBalances: [],
    postBalances: [],
    innerInstructions: [],
    logMessages: [],
    preTokenBalances: [],
    postTokenBalances: [],
    rewards: [],
    errorInfo: "",
  };
}

export const TransactionStatusMeta: MessageFns<TransactionStatusMeta> = {
  encode(message: TransactionStatusMeta, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isStatusErr !== false) {
      writer.uint32(8).bool(message.isStatusErr);
    }
    if (message.fee !== 0n) {
      if (BigInt.asUintN(64, message.fee) !== message.fee) {
        throw new globalThis.Error("value provided for field message.fee of type uint64 too large");
      }
      writer.uint32(16).uint64(message.fee);
    }
    writer.uint32(26).fork();
    for (const v of message.preBalances) {
      if (BigInt.asUintN(64, v) !== v) {
        throw new globalThis.Error("a value provided in array field preBalances of type uint64 is too large");
      }
      writer.uint64(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.postBalances) {
      if (BigInt.asUintN(64, v) !== v) {
        throw new globalThis.Error("a value provided in array field postBalances of type uint64 is too large");
      }
      writer.uint64(v);
    }
    writer.join();
    for (const v of message.innerInstructions) {
      InnerInstructions.encode(v!, writer.uint32(42).fork()).join();
    }
    for (const v of message.logMessages) {
      writer.uint32(50).string(v!);
    }
    for (const v of message.preTokenBalances) {
      TransactionTokenBalance.encode(v!, writer.uint32(58).fork()).join();
    }
    for (const v of message.postTokenBalances) {
      TransactionTokenBalance.encode(v!, writer.uint32(66).fork()).join();
    }
    for (const v of message.rewards) {
      Reward.encode(v!, writer.uint32(74).fork()).join();
    }
    if (message.errorInfo !== "") {
      writer.uint32(82).string(message.errorInfo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionStatusMeta {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionStatusMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isStatusErr = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.fee = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.preBalances.push(reader.uint64() as bigint);

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.preBalances.push(reader.uint64() as bigint);
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.postBalances.push(reader.uint64() as bigint);

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.postBalances.push(reader.uint64() as bigint);
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.innerInstructions.push(InnerInstructions.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.logMessages.push(reader.string());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.preTokenBalances.push(TransactionTokenBalance.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.postTokenBalances.push(TransactionTokenBalance.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.rewards.push(Reward.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.errorInfo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TransactionStatusMeta, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<TransactionStatusMeta | TransactionStatusMeta[]>
      | Iterable<TransactionStatusMeta | TransactionStatusMeta[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionStatusMeta.encode(p).finish()];
        }
      } else {
        yield* [TransactionStatusMeta.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TransactionStatusMeta>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TransactionStatusMeta> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionStatusMeta.decode(p)];
        }
      } else {
        yield* [TransactionStatusMeta.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TransactionStatusMeta {
    return {
      isStatusErr: isSet(object.isStatusErr) ? globalThis.Boolean(object.isStatusErr) : false,
      fee: isSet(object.fee) ? BigInt(object.fee) : 0n,
      preBalances: globalThis.Array.isArray(object?.preBalances) ? object.preBalances.map((e: any) => BigInt(e)) : [],
      postBalances: globalThis.Array.isArray(object?.postBalances)
        ? object.postBalances.map((e: any) => BigInt(e))
        : [],
      innerInstructions: globalThis.Array.isArray(object?.innerInstructions)
        ? object.innerInstructions.map((e: any) => InnerInstructions.fromJSON(e))
        : [],
      logMessages: globalThis.Array.isArray(object?.logMessages)
        ? object.logMessages.map((e: any) => globalThis.String(e))
        : [],
      preTokenBalances: globalThis.Array.isArray(object?.preTokenBalances)
        ? object.preTokenBalances.map((e: any) => TransactionTokenBalance.fromJSON(e))
        : [],
      postTokenBalances: globalThis.Array.isArray(object?.postTokenBalances)
        ? object.postTokenBalances.map((e: any) => TransactionTokenBalance.fromJSON(e))
        : [],
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => Reward.fromJSON(e)) : [],
      errorInfo: isSet(object.errorInfo) ? globalThis.String(object.errorInfo) : "",
    };
  },

  toJSON(message: TransactionStatusMeta): unknown {
    const obj: any = {};
    if (message.isStatusErr !== false) {
      obj.isStatusErr = message.isStatusErr;
    }
    if (message.fee !== 0n) {
      obj.fee = message.fee.toString();
    }
    if (message.preBalances?.length) {
      obj.preBalances = message.preBalances.map((e) => e.toString());
    }
    if (message.postBalances?.length) {
      obj.postBalances = message.postBalances.map((e) => e.toString());
    }
    if (message.innerInstructions?.length) {
      obj.innerInstructions = message.innerInstructions.map((e) => InnerInstructions.toJSON(e));
    }
    if (message.logMessages?.length) {
      obj.logMessages = message.logMessages;
    }
    if (message.preTokenBalances?.length) {
      obj.preTokenBalances = message.preTokenBalances.map((e) => TransactionTokenBalance.toJSON(e));
    }
    if (message.postTokenBalances?.length) {
      obj.postTokenBalances = message.postTokenBalances.map((e) => TransactionTokenBalance.toJSON(e));
    }
    if (message.rewards?.length) {
      obj.rewards = message.rewards.map((e) => Reward.toJSON(e));
    }
    if (message.errorInfo !== "") {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransactionStatusMeta>, I>>(base?: I): TransactionStatusMeta {
    return TransactionStatusMeta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionStatusMeta>, I>>(object: I): TransactionStatusMeta {
    const message = createBaseTransactionStatusMeta();
    message.isStatusErr = object.isStatusErr ?? false;
    message.fee = object.fee ?? 0n;
    message.preBalances = object.preBalances?.map((e) => e) || [];
    message.postBalances = object.postBalances?.map((e) => e) || [];
    message.innerInstructions = object.innerInstructions?.map((e) => InnerInstructions.fromPartial(e)) || [];
    message.logMessages = object.logMessages?.map((e) => e) || [];
    message.preTokenBalances = object.preTokenBalances?.map((e) => TransactionTokenBalance.fromPartial(e)) || [];
    message.postTokenBalances = object.postTokenBalances?.map((e) => TransactionTokenBalance.fromPartial(e)) || [];
    message.rewards = object.rewards?.map((e) => Reward.fromPartial(e)) || [];
    message.errorInfo = object.errorInfo ?? "";
    return message;
  },
};

function createBaseTransactionEventWrapper(): TransactionEventWrapper {
  return { streamType: 0, transaction: undefined };
}

export const TransactionEventWrapper: MessageFns<TransactionEventWrapper> = {
  encode(message: TransactionEventWrapper, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.streamType !== 0) {
      writer.uint32(8).int32(message.streamType);
    }
    if (message.transaction !== undefined) {
      TransactionEvent.encode(message.transaction, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionEventWrapper {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionEventWrapper();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.streamType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.transaction = TransactionEvent.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TransactionEventWrapper, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<TransactionEventWrapper | TransactionEventWrapper[]>
      | Iterable<TransactionEventWrapper | TransactionEventWrapper[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionEventWrapper.encode(p).finish()];
        }
      } else {
        yield* [TransactionEventWrapper.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TransactionEventWrapper>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TransactionEventWrapper> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionEventWrapper.decode(p)];
        }
      } else {
        yield* [TransactionEventWrapper.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TransactionEventWrapper {
    return {
      streamType: isSet(object.streamType) ? streamTypeFromJSON(object.streamType) : 0,
      transaction: isSet(object.transaction) ? TransactionEvent.fromJSON(object.transaction) : undefined,
    };
  },

  toJSON(message: TransactionEventWrapper): unknown {
    const obj: any = {};
    if (message.streamType !== 0) {
      obj.streamType = streamTypeToJSON(message.streamType);
    }
    if (message.transaction !== undefined) {
      obj.transaction = TransactionEvent.toJSON(message.transaction);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransactionEventWrapper>, I>>(base?: I): TransactionEventWrapper {
    return TransactionEventWrapper.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionEventWrapper>, I>>(object: I): TransactionEventWrapper {
    const message = createBaseTransactionEventWrapper();
    message.streamType = object.streamType ?? 0;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? TransactionEvent.fromPartial(object.transaction)
      : undefined;
    return message;
  },
};

function createBaseMessageWrapper(): MessageWrapper {
  return { account: undefined, slot: undefined, transaction: undefined };
}

export const MessageWrapper: MessageFns<MessageWrapper> = {
  encode(message: MessageWrapper, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.account !== undefined) {
      UpdateAccountEvent.encode(message.account, writer.uint32(10).fork()).join();
    }
    if (message.slot !== undefined) {
      SlotStatusEvent.encode(message.slot, writer.uint32(18).fork()).join();
    }
    if (message.transaction !== undefined) {
      TransactionEventWrapper.encode(message.transaction, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageWrapper {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageWrapper();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.account = UpdateAccountEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.slot = SlotStatusEvent.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.transaction = TransactionEventWrapper.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageWrapper, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<MessageWrapper | MessageWrapper[]> | Iterable<MessageWrapper | MessageWrapper[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageWrapper.encode(p).finish()];
        }
      } else {
        yield* [MessageWrapper.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageWrapper>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageWrapper> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageWrapper.decode(p)];
        }
      } else {
        yield* [MessageWrapper.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageWrapper {
    return {
      account: isSet(object.account) ? UpdateAccountEvent.fromJSON(object.account) : undefined,
      slot: isSet(object.slot) ? SlotStatusEvent.fromJSON(object.slot) : undefined,
      transaction: isSet(object.transaction) ? TransactionEventWrapper.fromJSON(object.transaction) : undefined,
    };
  },

  toJSON(message: MessageWrapper): unknown {
    const obj: any = {};
    if (message.account !== undefined) {
      obj.account = UpdateAccountEvent.toJSON(message.account);
    }
    if (message.slot !== undefined) {
      obj.slot = SlotStatusEvent.toJSON(message.slot);
    }
    if (message.transaction !== undefined) {
      obj.transaction = TransactionEventWrapper.toJSON(message.transaction);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageWrapper>, I>>(base?: I): MessageWrapper {
    return MessageWrapper.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageWrapper>, I>>(object: I): MessageWrapper {
    const message = createBaseMessageWrapper();
    message.account = (object.account !== undefined && object.account !== null)
      ? UpdateAccountEvent.fromPartial(object.account)
      : undefined;
    message.slot = (object.slot !== undefined && object.slot !== null)
      ? SlotStatusEvent.fromPartial(object.slot)
      : undefined;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? TransactionEventWrapper.fromPartial(object.transaction)
      : undefined;
    return message;
  },
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
