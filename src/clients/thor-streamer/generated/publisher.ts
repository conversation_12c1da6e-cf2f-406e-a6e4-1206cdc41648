// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: publisher.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientReadableStream,
  type handleServerStreamingCall,
  makeGenericClientConstructor,
  Metadata,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Empty } from "./google/protobuf/empty";

export interface StreamResponse {
  data: Buffer;
}

export interface SubscribeWalletRequest {
  /** Array of Base58 encoded wallet addresses, max 10 */
  walletAddress: string[];
}

function createBaseStreamResponse(): StreamResponse {
  return { data: Buffer.alloc(0) };
}

export const StreamResponse: MessageFns<StreamResponse> = {
  encode(message: StreamResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.data.length !== 0) {
      writer.uint32(10).bytes(message.data);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StreamResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStreamResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<StreamResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<StreamResponse | StreamResponse[]> | Iterable<StreamResponse | StreamResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [StreamResponse.encode(p).finish()];
        }
      } else {
        yield* [StreamResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, StreamResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<StreamResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [StreamResponse.decode(p)];
        }
      } else {
        yield* [StreamResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): StreamResponse {
    return { data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0) };
  },

  toJSON(message: StreamResponse): unknown {
    const obj: any = {};
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StreamResponse>, I>>(base?: I): StreamResponse {
    return StreamResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StreamResponse>, I>>(object: I): StreamResponse {
    const message = createBaseStreamResponse();
    message.data = object.data ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseSubscribeWalletRequest(): SubscribeWalletRequest {
  return { walletAddress: [] };
}

export const SubscribeWalletRequest: MessageFns<SubscribeWalletRequest> = {
  encode(message: SubscribeWalletRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.walletAddress) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeWalletRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeWalletRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.walletAddress.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeWalletRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeWalletRequest | SubscribeWalletRequest[]>
      | Iterable<SubscribeWalletRequest | SubscribeWalletRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeWalletRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeWalletRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeWalletRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeWalletRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeWalletRequest.decode(p)];
        }
      } else {
        yield* [SubscribeWalletRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeWalletRequest {
    return {
      walletAddress: globalThis.Array.isArray(object?.walletAddress)
        ? object.walletAddress.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: SubscribeWalletRequest): unknown {
    const obj: any = {};
    if (message.walletAddress?.length) {
      obj.walletAddress = message.walletAddress;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeWalletRequest>, I>>(base?: I): SubscribeWalletRequest {
    return SubscribeWalletRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeWalletRequest>, I>>(object: I): SubscribeWalletRequest {
    const message = createBaseSubscribeWalletRequest();
    message.walletAddress = object.walletAddress?.map((e) => e) || [];
    return message;
  },
};

export type EventPublisherService = typeof EventPublisherService;
export const EventPublisherService = {
  subscribeToTransactions: {
    path: "/publisher.EventPublisher/SubscribeToTransactions",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: Empty) => Buffer.from(Empty.encode(value).finish()),
    requestDeserialize: (value: Buffer) => Empty.decode(value),
    responseSerialize: (value: StreamResponse) => Buffer.from(StreamResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => StreamResponse.decode(value),
  },
  subscribeToAccountUpdates: {
    path: "/publisher.EventPublisher/SubscribeToAccountUpdates",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: Empty) => Buffer.from(Empty.encode(value).finish()),
    requestDeserialize: (value: Buffer) => Empty.decode(value),
    responseSerialize: (value: StreamResponse) => Buffer.from(StreamResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => StreamResponse.decode(value),
  },
  subscribeToSlotStatus: {
    path: "/publisher.EventPublisher/SubscribeToSlotStatus",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: Empty) => Buffer.from(Empty.encode(value).finish()),
    requestDeserialize: (value: Buffer) => Empty.decode(value),
    responseSerialize: (value: StreamResponse) => Buffer.from(StreamResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => StreamResponse.decode(value),
  },
  subscribeToWalletTransactions: {
    path: "/publisher.EventPublisher/SubscribeToWalletTransactions",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribeWalletRequest) => Buffer.from(SubscribeWalletRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeWalletRequest.decode(value),
    responseSerialize: (value: StreamResponse) => Buffer.from(StreamResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => StreamResponse.decode(value),
  },
} as const;

export interface EventPublisherServer extends UntypedServiceImplementation {
  subscribeToTransactions: handleServerStreamingCall<Empty, StreamResponse>;
  subscribeToAccountUpdates: handleServerStreamingCall<Empty, StreamResponse>;
  subscribeToSlotStatus: handleServerStreamingCall<Empty, StreamResponse>;
  subscribeToWalletTransactions: handleServerStreamingCall<SubscribeWalletRequest, StreamResponse>;
}

export interface EventPublisherClient extends Client {
  subscribeToTransactions(request: Empty, options?: Partial<CallOptions>): ClientReadableStream<StreamResponse>;
  subscribeToTransactions(
    request: Empty,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<StreamResponse>;
  subscribeToAccountUpdates(request: Empty, options?: Partial<CallOptions>): ClientReadableStream<StreamResponse>;
  subscribeToAccountUpdates(
    request: Empty,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<StreamResponse>;
  subscribeToSlotStatus(request: Empty, options?: Partial<CallOptions>): ClientReadableStream<StreamResponse>;
  subscribeToSlotStatus(
    request: Empty,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<StreamResponse>;
  subscribeToWalletTransactions(
    request: SubscribeWalletRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<StreamResponse>;
  subscribeToWalletTransactions(
    request: SubscribeWalletRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<StreamResponse>;
}

export const EventPublisherClient = makeGenericClientConstructor(
  EventPublisherService,
  "publisher.EventPublisher",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): EventPublisherClient;
  service: typeof EventPublisherService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
