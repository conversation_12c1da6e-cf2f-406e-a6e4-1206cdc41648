// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: jetstream.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientDuplexStream,
  type ClientOptions,
  type ClientUnaryCall,
  type handleBidiStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Timestamp } from "./google/protobuf/timestamp";

/** ============= Subscribe Request ============= */
export interface SubscribeRequest {
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  ping?: SubscribeRequestPing | undefined;
}

export interface SubscribeRequest_TransactionsEntry {
  key: string;
  value: SubscribeRequestFilterTransactions | undefined;
}

export interface SubscribeRequest_AccountsEntry {
  key: string;
  value: SubscribeRequestFilterAccounts | undefined;
}

export interface SubscribeRequestFilterTransactions {
  accountInclude: string[];
  accountExclude: string[];
  accountRequired: string[];
}

export interface SubscribeRequestFilterAccounts {
  account: string[];
  owner: string[];
  filters: SubscribeRequestFilterAccountsFilter[];
}

export interface SubscribeRequestFilterAccountsFilter {
  memcmp?: SubscribeRequestFilterAccountsFilterMemcmp | undefined;
  datasize?: bigint | undefined;
  lamports?: SubscribeRequestFilterAccountsFilterLamports | undefined;
}

export interface SubscribeRequestFilterAccountsFilterMemcmp {
  offset: bigint;
  bytes?: Buffer | undefined;
  base58?: string | undefined;
  base64?: string | undefined;
}

export interface SubscribeRequestFilterAccountsFilterLamports {
  eq?: bigint | undefined;
  ne?: bigint | undefined;
  lt?: bigint | undefined;
  gt?: bigint | undefined;
}

export interface SubscribeRequestPing {
  id: number;
}

/** ============= Subscribe Updates ============= */
export interface SubscribeUpdate {
  filters: string[];
  createdAt: Date | undefined;
  transaction?: SubscribeUpdateTransaction | undefined;
  account?: SubscribeUpdateAccount | undefined;
  ping?: SubscribeUpdatePing | undefined;
  pong?: SubscribeUpdatePong | undefined;
}

export interface SubscribeUpdateTransaction {
  transaction: SubscribeUpdateTransactionInfo | undefined;
  slot: bigint;
}

export interface SubscribeUpdateTransactionInfo {
  signature: Buffer;
  slot: bigint;
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
  recentBlockhash: Buffer;
  signatures: Buffer[];
  accountKeys: Buffer[];
  instructions: CompiledInstruction[];
  addressTableLookups: MessageAddressTableLookup[];
}

export interface SubscribeUpdateAccount {
  account: SubscribeUpdateAccountInfo | undefined;
  slot: bigint;
  isStartup: boolean;
}

export interface SubscribeUpdateAccountInfo {
  pubkey: Buffer;
  lamports: bigint;
  owner: Buffer;
  executable: boolean;
  rentEpoch: bigint;
  data: Buffer;
  writeVersion: bigint;
  txnSignature?: Buffer | undefined;
}

export interface SubscribeUpdatePing {
}

export interface SubscribeUpdatePong {
  id: number;
}

export interface MessageAddressTableLookup {
  accountKey: Buffer;
  writableIndexes: Buffer;
  readonlyIndexes: Buffer;
}

export interface CompiledInstruction {
  programIdIndex: number;
  accounts: Buffer;
  data: Buffer;
}

/** ============= Parsed Instruction Messages ============= */
export interface SubscribeParsedRequest {
  ping?: SubscribeRequestPing | undefined;
}

export interface Instruction {
  initialize?: Initialize | undefined;
  setParams?: SetParams | undefined;
  create?: Create | undefined;
  buy?: Buy | undefined;
  sell?: Sell | undefined;
  withdraw?: Withdraw | undefined;
}

export interface Initialize {
}

export interface SetParams {
  feeRecipient: Buffer;
  initialVirtualTokenReserves: bigint;
  initialVirtualSolReserves: bigint;
  initialRealTokenReserves: bigint;
  tokenTotalSupply: bigint;
  feeBasisPoints: bigint;
}

export interface Create {
  name: string;
  symbol: string;
  uri: string;
}

export interface Buy {
  amount: bigint;
  maxSolCost: bigint;
}

export interface Sell {
  amount: bigint;
  minSolOutput: bigint;
}

export interface Withdraw {
}

/** For SubscribeParsed RPC */
export interface SubscribeUpdateParsedTransaction {
  signature: Buffer;
  slot: bigint;
  account: SubscribeUpdateAccount | undefined;
  recentBlockhash: Buffer;
  signatures: Buffer[];
  instructions: Instruction[];
}

/** ============= Non-streaming Methods ============= */
export interface PingRequest {
  count: number;
}

export interface PongResponse {
  count: number;
}

export interface GetVersionRequest {
}

export interface GetVersionResponse {
  version: string;
}

export interface GetSlotResponse {
  slot: bigint;
}

function createBaseSubscribeRequest(): SubscribeRequest {
  return { transactions: {}, accounts: {}, ping: undefined };
}

export const SubscribeRequest: MessageFns<SubscribeRequest> = {
  encode(message: SubscribeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.transactions).forEach(([key, value]) => {
      SubscribeRequest_TransactionsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.accounts).forEach(([key, value]) => {
      SubscribeRequest_AccountsEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.ping !== undefined) {
      SubscribeRequestPing.encode(message.ping, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = SubscribeRequest_TransactionsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.transactions[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = SubscribeRequest_AccountsEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.accounts[entry2.key] = entry2.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.ping = SubscribeRequestPing.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SubscribeRequest | SubscribeRequest[]> | Iterable<SubscribeRequest | SubscribeRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest.decode(p)];
        }
      } else {
        yield* [SubscribeRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest {
    return {
      transactions: isObject(object.transactions)
        ? Object.entries(object.transactions).reduce<{ [key: string]: SubscribeRequestFilterTransactions }>(
          (acc, [key, value]) => {
            acc[key] = SubscribeRequestFilterTransactions.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      accounts: isObject(object.accounts)
        ? Object.entries(object.accounts).reduce<{ [key: string]: SubscribeRequestFilterAccounts }>(
          (acc, [key, value]) => {
            acc[key] = SubscribeRequestFilterAccounts.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      ping: isSet(object.ping) ? SubscribeRequestPing.fromJSON(object.ping) : undefined,
    };
  },

  toJSON(message: SubscribeRequest): unknown {
    const obj: any = {};
    if (message.transactions) {
      const entries = Object.entries(message.transactions);
      if (entries.length > 0) {
        obj.transactions = {};
        entries.forEach(([k, v]) => {
          obj.transactions[k] = SubscribeRequestFilterTransactions.toJSON(v);
        });
      }
    }
    if (message.accounts) {
      const entries = Object.entries(message.accounts);
      if (entries.length > 0) {
        obj.accounts = {};
        entries.forEach(([k, v]) => {
          obj.accounts[k] = SubscribeRequestFilterAccounts.toJSON(v);
        });
      }
    }
    if (message.ping !== undefined) {
      obj.ping = SubscribeRequestPing.toJSON(message.ping);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest>, I>>(base?: I): SubscribeRequest {
    return SubscribeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest>, I>>(object: I): SubscribeRequest {
    const message = createBaseSubscribeRequest();
    message.transactions = Object.entries(object.transactions ?? {}).reduce<
      { [key: string]: SubscribeRequestFilterTransactions }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = SubscribeRequestFilterTransactions.fromPartial(value);
      }
      return acc;
    }, {});
    message.accounts = Object.entries(object.accounts ?? {}).reduce<{ [key: string]: SubscribeRequestFilterAccounts }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SubscribeRequestFilterAccounts.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.ping = (object.ping !== undefined && object.ping !== null)
      ? SubscribeRequestPing.fromPartial(object.ping)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_TransactionsEntry(): SubscribeRequest_TransactionsEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_TransactionsEntry: MessageFns<SubscribeRequest_TransactionsEntry> = {
  encode(message: SubscribeRequest_TransactionsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterTransactions.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_TransactionsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_TransactionsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterTransactions.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_TransactionsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_TransactionsEntry | SubscribeRequest_TransactionsEntry[]>
      | Iterable<SubscribeRequest_TransactionsEntry | SubscribeRequest_TransactionsEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_TransactionsEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_TransactionsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_TransactionsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_TransactionsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_TransactionsEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_TransactionsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_TransactionsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterTransactions.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_TransactionsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterTransactions.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_TransactionsEntry>, I>>(
    base?: I,
  ): SubscribeRequest_TransactionsEntry {
    return SubscribeRequest_TransactionsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_TransactionsEntry>, I>>(
    object: I,
  ): SubscribeRequest_TransactionsEntry {
    const message = createBaseSubscribeRequest_TransactionsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterTransactions.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_AccountsEntry(): SubscribeRequest_AccountsEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_AccountsEntry: MessageFns<SubscribeRequest_AccountsEntry> = {
  encode(message: SubscribeRequest_AccountsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterAccounts.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_AccountsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_AccountsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterAccounts.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_AccountsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_AccountsEntry | SubscribeRequest_AccountsEntry[]>
      | Iterable<SubscribeRequest_AccountsEntry | SubscribeRequest_AccountsEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_AccountsEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_AccountsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_AccountsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_AccountsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_AccountsEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_AccountsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_AccountsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterAccounts.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_AccountsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterAccounts.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_AccountsEntry>, I>>(base?: I): SubscribeRequest_AccountsEntry {
    return SubscribeRequest_AccountsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_AccountsEntry>, I>>(
    object: I,
  ): SubscribeRequest_AccountsEntry {
    const message = createBaseSubscribeRequest_AccountsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterAccounts.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterTransactions(): SubscribeRequestFilterTransactions {
  return { accountInclude: [], accountExclude: [], accountRequired: [] };
}

export const SubscribeRequestFilterTransactions: MessageFns<SubscribeRequestFilterTransactions> = {
  encode(message: SubscribeRequestFilterTransactions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accountInclude) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.accountExclude) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.accountRequired) {
      writer.uint32(26).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterTransactions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterTransactions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountInclude.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountExclude.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accountRequired.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterTransactions, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterTransactions | SubscribeRequestFilterTransactions[]>
      | Iterable<SubscribeRequestFilterTransactions | SubscribeRequestFilterTransactions[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterTransactions.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterTransactions.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterTransactions>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterTransactions> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterTransactions.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterTransactions.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterTransactions {
    return {
      accountInclude: globalThis.Array.isArray(object?.accountInclude)
        ? object.accountInclude.map((e: any) => globalThis.String(e))
        : [],
      accountExclude: globalThis.Array.isArray(object?.accountExclude)
        ? object.accountExclude.map((e: any) => globalThis.String(e))
        : [],
      accountRequired: globalThis.Array.isArray(object?.accountRequired)
        ? object.accountRequired.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: SubscribeRequestFilterTransactions): unknown {
    const obj: any = {};
    if (message.accountInclude?.length) {
      obj.accountInclude = message.accountInclude;
    }
    if (message.accountExclude?.length) {
      obj.accountExclude = message.accountExclude;
    }
    if (message.accountRequired?.length) {
      obj.accountRequired = message.accountRequired;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterTransactions>, I>>(
    base?: I,
  ): SubscribeRequestFilterTransactions {
    return SubscribeRequestFilterTransactions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterTransactions>, I>>(
    object: I,
  ): SubscribeRequestFilterTransactions {
    const message = createBaseSubscribeRequestFilterTransactions();
    message.accountInclude = object.accountInclude?.map((e) => e) || [];
    message.accountExclude = object.accountExclude?.map((e) => e) || [];
    message.accountRequired = object.accountRequired?.map((e) => e) || [];
    return message;
  },
};

function createBaseSubscribeRequestFilterAccounts(): SubscribeRequestFilterAccounts {
  return { account: [], owner: [], filters: [] };
}

export const SubscribeRequestFilterAccounts: MessageFns<SubscribeRequestFilterAccounts> = {
  encode(message: SubscribeRequestFilterAccounts, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.account) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.owner) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.filters) {
      SubscribeRequestFilterAccountsFilter.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccounts {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccounts();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.account.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.owner.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filters.push(SubscribeRequestFilterAccountsFilter.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccounts, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccounts | SubscribeRequestFilterAccounts[]>
      | Iterable<SubscribeRequestFilterAccounts | SubscribeRequestFilterAccounts[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccounts.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccounts.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccounts>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccounts> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccounts.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccounts.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccounts {
    return {
      account: globalThis.Array.isArray(object?.account) ? object.account.map((e: any) => globalThis.String(e)) : [],
      owner: globalThis.Array.isArray(object?.owner) ? object.owner.map((e: any) => globalThis.String(e)) : [],
      filters: globalThis.Array.isArray(object?.filters)
        ? object.filters.map((e: any) => SubscribeRequestFilterAccountsFilter.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SubscribeRequestFilterAccounts): unknown {
    const obj: any = {};
    if (message.account?.length) {
      obj.account = message.account;
    }
    if (message.owner?.length) {
      obj.owner = message.owner;
    }
    if (message.filters?.length) {
      obj.filters = message.filters.map((e) => SubscribeRequestFilterAccountsFilter.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccounts>, I>>(base?: I): SubscribeRequestFilterAccounts {
    return SubscribeRequestFilterAccounts.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccounts>, I>>(
    object: I,
  ): SubscribeRequestFilterAccounts {
    const message = createBaseSubscribeRequestFilterAccounts();
    message.account = object.account?.map((e) => e) || [];
    message.owner = object.owner?.map((e) => e) || [];
    message.filters = object.filters?.map((e) => SubscribeRequestFilterAccountsFilter.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSubscribeRequestFilterAccountsFilter(): SubscribeRequestFilterAccountsFilter {
  return { memcmp: undefined, datasize: undefined, lamports: undefined };
}

export const SubscribeRequestFilterAccountsFilter: MessageFns<SubscribeRequestFilterAccountsFilter> = {
  encode(message: SubscribeRequestFilterAccountsFilter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memcmp !== undefined) {
      SubscribeRequestFilterAccountsFilterMemcmp.encode(message.memcmp, writer.uint32(10).fork()).join();
    }
    if (message.datasize !== undefined) {
      if (BigInt.asUintN(64, message.datasize) !== message.datasize) {
        throw new globalThis.Error("value provided for field message.datasize of type uint64 too large");
      }
      writer.uint32(16).uint64(message.datasize);
    }
    if (message.lamports !== undefined) {
      SubscribeRequestFilterAccountsFilterLamports.encode(message.lamports, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccountsFilter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccountsFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memcmp = SubscribeRequestFilterAccountsFilterMemcmp.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.datasize = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.lamports = SubscribeRequestFilterAccountsFilterLamports.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccountsFilter, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccountsFilter | SubscribeRequestFilterAccountsFilter[]>
      | Iterable<SubscribeRequestFilterAccountsFilter | SubscribeRequestFilterAccountsFilter[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilter.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilter.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccountsFilter>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccountsFilter> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilter.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilter.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccountsFilter {
    return {
      memcmp: isSet(object.memcmp) ? SubscribeRequestFilterAccountsFilterMemcmp.fromJSON(object.memcmp) : undefined,
      datasize: isSet(object.datasize) ? BigInt(object.datasize) : undefined,
      lamports: isSet(object.lamports)
        ? SubscribeRequestFilterAccountsFilterLamports.fromJSON(object.lamports)
        : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterAccountsFilter): unknown {
    const obj: any = {};
    if (message.memcmp !== undefined) {
      obj.memcmp = SubscribeRequestFilterAccountsFilterMemcmp.toJSON(message.memcmp);
    }
    if (message.datasize !== undefined) {
      obj.datasize = message.datasize.toString();
    }
    if (message.lamports !== undefined) {
      obj.lamports = SubscribeRequestFilterAccountsFilterLamports.toJSON(message.lamports);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilter>, I>>(
    base?: I,
  ): SubscribeRequestFilterAccountsFilter {
    return SubscribeRequestFilterAccountsFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilter>, I>>(
    object: I,
  ): SubscribeRequestFilterAccountsFilter {
    const message = createBaseSubscribeRequestFilterAccountsFilter();
    message.memcmp = (object.memcmp !== undefined && object.memcmp !== null)
      ? SubscribeRequestFilterAccountsFilterMemcmp.fromPartial(object.memcmp)
      : undefined;
    message.datasize = object.datasize ?? undefined;
    message.lamports = (object.lamports !== undefined && object.lamports !== null)
      ? SubscribeRequestFilterAccountsFilterLamports.fromPartial(object.lamports)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterAccountsFilterMemcmp(): SubscribeRequestFilterAccountsFilterMemcmp {
  return { offset: 0n, bytes: undefined, base58: undefined, base64: undefined };
}

export const SubscribeRequestFilterAccountsFilterMemcmp: MessageFns<SubscribeRequestFilterAccountsFilterMemcmp> = {
  encode(message: SubscribeRequestFilterAccountsFilterMemcmp, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.offset !== 0n) {
      if (BigInt.asUintN(64, message.offset) !== message.offset) {
        throw new globalThis.Error("value provided for field message.offset of type uint64 too large");
      }
      writer.uint32(8).uint64(message.offset);
    }
    if (message.bytes !== undefined) {
      writer.uint32(18).bytes(message.bytes);
    }
    if (message.base58 !== undefined) {
      writer.uint32(26).string(message.base58);
    }
    if (message.base64 !== undefined) {
      writer.uint32(34).string(message.base64);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccountsFilterMemcmp {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccountsFilterMemcmp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.offset = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bytes = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.base58 = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.base64 = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccountsFilterMemcmp, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccountsFilterMemcmp | SubscribeRequestFilterAccountsFilterMemcmp[]>
      | Iterable<SubscribeRequestFilterAccountsFilterMemcmp | SubscribeRequestFilterAccountsFilterMemcmp[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterMemcmp.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterMemcmp.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccountsFilterMemcmp>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccountsFilterMemcmp> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterMemcmp.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterMemcmp.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccountsFilterMemcmp {
    return {
      offset: isSet(object.offset) ? BigInt(object.offset) : 0n,
      bytes: isSet(object.bytes) ? Buffer.from(bytesFromBase64(object.bytes)) : undefined,
      base58: isSet(object.base58) ? globalThis.String(object.base58) : undefined,
      base64: isSet(object.base64) ? globalThis.String(object.base64) : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterAccountsFilterMemcmp): unknown {
    const obj: any = {};
    if (message.offset !== 0n) {
      obj.offset = message.offset.toString();
    }
    if (message.bytes !== undefined) {
      obj.bytes = base64FromBytes(message.bytes);
    }
    if (message.base58 !== undefined) {
      obj.base58 = message.base58;
    }
    if (message.base64 !== undefined) {
      obj.base64 = message.base64;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterMemcmp>, I>>(
    base?: I,
  ): SubscribeRequestFilterAccountsFilterMemcmp {
    return SubscribeRequestFilterAccountsFilterMemcmp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterMemcmp>, I>>(
    object: I,
  ): SubscribeRequestFilterAccountsFilterMemcmp {
    const message = createBaseSubscribeRequestFilterAccountsFilterMemcmp();
    message.offset = object.offset ?? 0n;
    message.bytes = object.bytes ?? undefined;
    message.base58 = object.base58 ?? undefined;
    message.base64 = object.base64 ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterAccountsFilterLamports(): SubscribeRequestFilterAccountsFilterLamports {
  return { eq: undefined, ne: undefined, lt: undefined, gt: undefined };
}

export const SubscribeRequestFilterAccountsFilterLamports: MessageFns<SubscribeRequestFilterAccountsFilterLamports> = {
  encode(
    message: SubscribeRequestFilterAccountsFilterLamports,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.eq !== undefined) {
      if (BigInt.asUintN(64, message.eq) !== message.eq) {
        throw new globalThis.Error("value provided for field message.eq of type uint64 too large");
      }
      writer.uint32(8).uint64(message.eq);
    }
    if (message.ne !== undefined) {
      if (BigInt.asUintN(64, message.ne) !== message.ne) {
        throw new globalThis.Error("value provided for field message.ne of type uint64 too large");
      }
      writer.uint32(16).uint64(message.ne);
    }
    if (message.lt !== undefined) {
      if (BigInt.asUintN(64, message.lt) !== message.lt) {
        throw new globalThis.Error("value provided for field message.lt of type uint64 too large");
      }
      writer.uint32(24).uint64(message.lt);
    }
    if (message.gt !== undefined) {
      if (BigInt.asUintN(64, message.gt) !== message.gt) {
        throw new globalThis.Error("value provided for field message.gt of type uint64 too large");
      }
      writer.uint32(32).uint64(message.gt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccountsFilterLamports {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccountsFilterLamports();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.eq = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.ne = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lt = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.gt = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccountsFilterLamports, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccountsFilterLamports | SubscribeRequestFilterAccountsFilterLamports[]>
      | Iterable<SubscribeRequestFilterAccountsFilterLamports | SubscribeRequestFilterAccountsFilterLamports[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterLamports.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterLamports.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccountsFilterLamports>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccountsFilterLamports> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterLamports.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterLamports.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccountsFilterLamports {
    return {
      eq: isSet(object.eq) ? BigInt(object.eq) : undefined,
      ne: isSet(object.ne) ? BigInt(object.ne) : undefined,
      lt: isSet(object.lt) ? BigInt(object.lt) : undefined,
      gt: isSet(object.gt) ? BigInt(object.gt) : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterAccountsFilterLamports): unknown {
    const obj: any = {};
    if (message.eq !== undefined) {
      obj.eq = message.eq.toString();
    }
    if (message.ne !== undefined) {
      obj.ne = message.ne.toString();
    }
    if (message.lt !== undefined) {
      obj.lt = message.lt.toString();
    }
    if (message.gt !== undefined) {
      obj.gt = message.gt.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterLamports>, I>>(
    base?: I,
  ): SubscribeRequestFilterAccountsFilterLamports {
    return SubscribeRequestFilterAccountsFilterLamports.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterLamports>, I>>(
    object: I,
  ): SubscribeRequestFilterAccountsFilterLamports {
    const message = createBaseSubscribeRequestFilterAccountsFilterLamports();
    message.eq = object.eq ?? undefined;
    message.ne = object.ne ?? undefined;
    message.lt = object.lt ?? undefined;
    message.gt = object.gt ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequestPing(): SubscribeRequestPing {
  return { id: 0 };
}

export const SubscribeRequestPing: MessageFns<SubscribeRequestPing> = {
  encode(message: SubscribeRequestPing, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestPing {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestPing();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestPing, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestPing | SubscribeRequestPing[]>
      | Iterable<SubscribeRequestPing | SubscribeRequestPing[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestPing.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestPing.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestPing>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestPing> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestPing.decode(p)];
        }
      } else {
        yield* [SubscribeRequestPing.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestPing {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  toJSON(message: SubscribeRequestPing): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestPing>, I>>(base?: I): SubscribeRequestPing {
    return SubscribeRequestPing.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestPing>, I>>(object: I): SubscribeRequestPing {
    const message = createBaseSubscribeRequestPing();
    message.id = object.id ?? 0;
    return message;
  },
};

function createBaseSubscribeUpdate(): SubscribeUpdate {
  return {
    filters: [],
    createdAt: undefined,
    transaction: undefined,
    account: undefined,
    ping: undefined,
    pong: undefined,
  };
}

export const SubscribeUpdate: MessageFns<SubscribeUpdate> = {
  encode(message: SubscribeUpdate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.filters) {
      writer.uint32(10).string(v!);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(18).fork()).join();
    }
    if (message.transaction !== undefined) {
      SubscribeUpdateTransaction.encode(message.transaction, writer.uint32(26).fork()).join();
    }
    if (message.account !== undefined) {
      SubscribeUpdateAccount.encode(message.account, writer.uint32(34).fork()).join();
    }
    if (message.ping !== undefined) {
      SubscribeUpdatePing.encode(message.ping, writer.uint32(42).fork()).join();
    }
    if (message.pong !== undefined) {
      SubscribeUpdatePong.encode(message.pong, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filters.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.transaction = SubscribeUpdateTransaction.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.account = SubscribeUpdateAccount.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.ping = SubscribeUpdatePing.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.pong = SubscribeUpdatePong.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdate, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SubscribeUpdate | SubscribeUpdate[]> | Iterable<SubscribeUpdate | SubscribeUpdate[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdate.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdate.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdate>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdate> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdate.decode(p)];
        }
      } else {
        yield* [SubscribeUpdate.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdate {
    return {
      filters: globalThis.Array.isArray(object?.filters) ? object.filters.map((e: any) => globalThis.String(e)) : [],
      createdAt: isSet(object.createdAt) ? fromJsonTimestamp(object.createdAt) : undefined,
      transaction: isSet(object.transaction) ? SubscribeUpdateTransaction.fromJSON(object.transaction) : undefined,
      account: isSet(object.account) ? SubscribeUpdateAccount.fromJSON(object.account) : undefined,
      ping: isSet(object.ping) ? SubscribeUpdatePing.fromJSON(object.ping) : undefined,
      pong: isSet(object.pong) ? SubscribeUpdatePong.fromJSON(object.pong) : undefined,
    };
  },

  toJSON(message: SubscribeUpdate): unknown {
    const obj: any = {};
    if (message.filters?.length) {
      obj.filters = message.filters;
    }
    if (message.createdAt !== undefined) {
      obj.createdAt = message.createdAt.toISOString();
    }
    if (message.transaction !== undefined) {
      obj.transaction = SubscribeUpdateTransaction.toJSON(message.transaction);
    }
    if (message.account !== undefined) {
      obj.account = SubscribeUpdateAccount.toJSON(message.account);
    }
    if (message.ping !== undefined) {
      obj.ping = SubscribeUpdatePing.toJSON(message.ping);
    }
    if (message.pong !== undefined) {
      obj.pong = SubscribeUpdatePong.toJSON(message.pong);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdate>, I>>(base?: I): SubscribeUpdate {
    return SubscribeUpdate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdate>, I>>(object: I): SubscribeUpdate {
    const message = createBaseSubscribeUpdate();
    message.filters = object.filters?.map((e) => e) || [];
    message.createdAt = object.createdAt ?? undefined;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? SubscribeUpdateTransaction.fromPartial(object.transaction)
      : undefined;
    message.account = (object.account !== undefined && object.account !== null)
      ? SubscribeUpdateAccount.fromPartial(object.account)
      : undefined;
    message.ping = (object.ping !== undefined && object.ping !== null)
      ? SubscribeUpdatePing.fromPartial(object.ping)
      : undefined;
    message.pong = (object.pong !== undefined && object.pong !== null)
      ? SubscribeUpdatePong.fromPartial(object.pong)
      : undefined;
    return message;
  },
};

function createBaseSubscribeUpdateTransaction(): SubscribeUpdateTransaction {
  return { transaction: undefined, slot: 0n };
}

export const SubscribeUpdateTransaction: MessageFns<SubscribeUpdateTransaction> = {
  encode(message: SubscribeUpdateTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transaction !== undefined) {
      SubscribeUpdateTransactionInfo.encode(message.transaction, writer.uint32(10).fork()).join();
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transaction = SubscribeUpdateTransactionInfo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateTransaction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateTransaction | SubscribeUpdateTransaction[]>
      | Iterable<SubscribeUpdateTransaction | SubscribeUpdateTransaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransaction.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateTransaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateTransaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateTransaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransaction.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateTransaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateTransaction {
    return {
      transaction: isSet(object.transaction) ? SubscribeUpdateTransactionInfo.fromJSON(object.transaction) : undefined,
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
    };
  },

  toJSON(message: SubscribeUpdateTransaction): unknown {
    const obj: any = {};
    if (message.transaction !== undefined) {
      obj.transaction = SubscribeUpdateTransactionInfo.toJSON(message.transaction);
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateTransaction>, I>>(base?: I): SubscribeUpdateTransaction {
    return SubscribeUpdateTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateTransaction>, I>>(object: I): SubscribeUpdateTransaction {
    const message = createBaseSubscribeUpdateTransaction();
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? SubscribeUpdateTransactionInfo.fromPartial(object.transaction)
      : undefined;
    message.slot = object.slot ?? 0n;
    return message;
  },
};

function createBaseSubscribeUpdateTransactionInfo(): SubscribeUpdateTransactionInfo {
  return {
    signature: Buffer.alloc(0),
    slot: 0n,
    numRequiredSignatures: 0,
    numReadonlySignedAccounts: 0,
    numReadonlyUnsignedAccounts: 0,
    recentBlockhash: Buffer.alloc(0),
    signatures: [],
    accountKeys: [],
    instructions: [],
    addressTableLookups: [],
  };
}

export const SubscribeUpdateTransactionInfo: MessageFns<SubscribeUpdateTransactionInfo> = {
  encode(message: SubscribeUpdateTransactionInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.signature.length !== 0) {
      writer.uint32(10).bytes(message.signature);
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    if (message.numRequiredSignatures !== 0) {
      writer.uint32(24).uint32(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      writer.uint32(32).uint32(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      writer.uint32(40).uint32(message.numReadonlyUnsignedAccounts);
    }
    if (message.recentBlockhash.length !== 0) {
      writer.uint32(50).bytes(message.recentBlockhash);
    }
    for (const v of message.signatures) {
      writer.uint32(58).bytes(v!);
    }
    for (const v of message.accountKeys) {
      writer.uint32(66).bytes(v!);
    }
    for (const v of message.instructions) {
      CompiledInstruction.encode(v!, writer.uint32(74).fork()).join();
    }
    for (const v of message.addressTableLookups) {
      MessageAddressTableLookup.encode(v!, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateTransactionInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateTransactionInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.signature = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.numRequiredSignatures = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.numReadonlySignedAccounts = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.numReadonlyUnsignedAccounts = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.recentBlockhash = Buffer.from(reader.bytes());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.signatures.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.accountKeys.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.instructions.push(CompiledInstruction.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.addressTableLookups.push(MessageAddressTableLookup.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateTransactionInfo, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateTransactionInfo | SubscribeUpdateTransactionInfo[]>
      | Iterable<SubscribeUpdateTransactionInfo | SubscribeUpdateTransactionInfo[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransactionInfo.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateTransactionInfo.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateTransactionInfo>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateTransactionInfo> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransactionInfo.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateTransactionInfo.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateTransactionInfo {
    return {
      signature: isSet(object.signature) ? Buffer.from(bytesFromBase64(object.signature)) : Buffer.alloc(0),
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      numRequiredSignatures: isSet(object.numRequiredSignatures) ? globalThis.Number(object.numRequiredSignatures) : 0,
      numReadonlySignedAccounts: isSet(object.numReadonlySignedAccounts)
        ? globalThis.Number(object.numReadonlySignedAccounts)
        : 0,
      numReadonlyUnsignedAccounts: isSet(object.numReadonlyUnsignedAccounts)
        ? globalThis.Number(object.numReadonlyUnsignedAccounts)
        : 0,
      recentBlockhash: isSet(object.recentBlockhash)
        ? Buffer.from(bytesFromBase64(object.recentBlockhash))
        : Buffer.alloc(0),
      signatures: globalThis.Array.isArray(object?.signatures)
        ? object.signatures.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      accountKeys: globalThis.Array.isArray(object?.accountKeys)
        ? object.accountKeys.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => CompiledInstruction.fromJSON(e))
        : [],
      addressTableLookups: globalThis.Array.isArray(object?.addressTableLookups)
        ? object.addressTableLookups.map((e: any) => MessageAddressTableLookup.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SubscribeUpdateTransactionInfo): unknown {
    const obj: any = {};
    if (message.signature.length !== 0) {
      obj.signature = base64FromBytes(message.signature);
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.numRequiredSignatures !== 0) {
      obj.numRequiredSignatures = Math.round(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      obj.numReadonlySignedAccounts = Math.round(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      obj.numReadonlyUnsignedAccounts = Math.round(message.numReadonlyUnsignedAccounts);
    }
    if (message.recentBlockhash.length !== 0) {
      obj.recentBlockhash = base64FromBytes(message.recentBlockhash);
    }
    if (message.signatures?.length) {
      obj.signatures = message.signatures.map((e) => base64FromBytes(e));
    }
    if (message.accountKeys?.length) {
      obj.accountKeys = message.accountKeys.map((e) => base64FromBytes(e));
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => CompiledInstruction.toJSON(e));
    }
    if (message.addressTableLookups?.length) {
      obj.addressTableLookups = message.addressTableLookups.map((e) => MessageAddressTableLookup.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateTransactionInfo>, I>>(base?: I): SubscribeUpdateTransactionInfo {
    return SubscribeUpdateTransactionInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateTransactionInfo>, I>>(
    object: I,
  ): SubscribeUpdateTransactionInfo {
    const message = createBaseSubscribeUpdateTransactionInfo();
    message.signature = object.signature ?? Buffer.alloc(0);
    message.slot = object.slot ?? 0n;
    message.numRequiredSignatures = object.numRequiredSignatures ?? 0;
    message.numReadonlySignedAccounts = object.numReadonlySignedAccounts ?? 0;
    message.numReadonlyUnsignedAccounts = object.numReadonlyUnsignedAccounts ?? 0;
    message.recentBlockhash = object.recentBlockhash ?? Buffer.alloc(0);
    message.signatures = object.signatures?.map((e) => e) || [];
    message.accountKeys = object.accountKeys?.map((e) => e) || [];
    message.instructions = object.instructions?.map((e) => CompiledInstruction.fromPartial(e)) || [];
    message.addressTableLookups = object.addressTableLookups?.map((e) => MessageAddressTableLookup.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseSubscribeUpdateAccount(): SubscribeUpdateAccount {
  return { account: undefined, slot: 0n, isStartup: false };
}

export const SubscribeUpdateAccount: MessageFns<SubscribeUpdateAccount> = {
  encode(message: SubscribeUpdateAccount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.account !== undefined) {
      SubscribeUpdateAccountInfo.encode(message.account, writer.uint32(10).fork()).join();
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    if (message.isStartup !== false) {
      writer.uint32(24).bool(message.isStartup);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateAccount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateAccount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.account = SubscribeUpdateAccountInfo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isStartup = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateAccount, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateAccount | SubscribeUpdateAccount[]>
      | Iterable<SubscribeUpdateAccount | SubscribeUpdateAccount[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccount.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateAccount.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateAccount>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateAccount> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccount.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateAccount.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateAccount {
    return {
      account: isSet(object.account) ? SubscribeUpdateAccountInfo.fromJSON(object.account) : undefined,
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      isStartup: isSet(object.isStartup) ? globalThis.Boolean(object.isStartup) : false,
    };
  },

  toJSON(message: SubscribeUpdateAccount): unknown {
    const obj: any = {};
    if (message.account !== undefined) {
      obj.account = SubscribeUpdateAccountInfo.toJSON(message.account);
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.isStartup !== false) {
      obj.isStartup = message.isStartup;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateAccount>, I>>(base?: I): SubscribeUpdateAccount {
    return SubscribeUpdateAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateAccount>, I>>(object: I): SubscribeUpdateAccount {
    const message = createBaseSubscribeUpdateAccount();
    message.account = (object.account !== undefined && object.account !== null)
      ? SubscribeUpdateAccountInfo.fromPartial(object.account)
      : undefined;
    message.slot = object.slot ?? 0n;
    message.isStartup = object.isStartup ?? false;
    return message;
  },
};

function createBaseSubscribeUpdateAccountInfo(): SubscribeUpdateAccountInfo {
  return {
    pubkey: Buffer.alloc(0),
    lamports: 0n,
    owner: Buffer.alloc(0),
    executable: false,
    rentEpoch: 0n,
    data: Buffer.alloc(0),
    writeVersion: 0n,
    txnSignature: undefined,
  };
}

export const SubscribeUpdateAccountInfo: MessageFns<SubscribeUpdateAccountInfo> = {
  encode(message: SubscribeUpdateAccountInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pubkey.length !== 0) {
      writer.uint32(10).bytes(message.pubkey);
    }
    if (message.lamports !== 0n) {
      if (BigInt.asUintN(64, message.lamports) !== message.lamports) {
        throw new globalThis.Error("value provided for field message.lamports of type uint64 too large");
      }
      writer.uint32(16).uint64(message.lamports);
    }
    if (message.owner.length !== 0) {
      writer.uint32(26).bytes(message.owner);
    }
    if (message.executable !== false) {
      writer.uint32(32).bool(message.executable);
    }
    if (message.rentEpoch !== 0n) {
      if (BigInt.asUintN(64, message.rentEpoch) !== message.rentEpoch) {
        throw new globalThis.Error("value provided for field message.rentEpoch of type uint64 too large");
      }
      writer.uint32(40).uint64(message.rentEpoch);
    }
    if (message.data.length !== 0) {
      writer.uint32(50).bytes(message.data);
    }
    if (message.writeVersion !== 0n) {
      if (BigInt.asUintN(64, message.writeVersion) !== message.writeVersion) {
        throw new globalThis.Error("value provided for field message.writeVersion of type uint64 too large");
      }
      writer.uint32(56).uint64(message.writeVersion);
    }
    if (message.txnSignature !== undefined) {
      writer.uint32(66).bytes(message.txnSignature);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateAccountInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateAccountInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pubkey = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lamports = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.owner = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.executable = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.rentEpoch = reader.uint64() as bigint;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.writeVersion = reader.uint64() as bigint;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.txnSignature = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateAccountInfo, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateAccountInfo | SubscribeUpdateAccountInfo[]>
      | Iterable<SubscribeUpdateAccountInfo | SubscribeUpdateAccountInfo[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccountInfo.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateAccountInfo.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateAccountInfo>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateAccountInfo> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccountInfo.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateAccountInfo.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateAccountInfo {
    return {
      pubkey: isSet(object.pubkey) ? Buffer.from(bytesFromBase64(object.pubkey)) : Buffer.alloc(0),
      lamports: isSet(object.lamports) ? BigInt(object.lamports) : 0n,
      owner: isSet(object.owner) ? Buffer.from(bytesFromBase64(object.owner)) : Buffer.alloc(0),
      executable: isSet(object.executable) ? globalThis.Boolean(object.executable) : false,
      rentEpoch: isSet(object.rentEpoch) ? BigInt(object.rentEpoch) : 0n,
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
      writeVersion: isSet(object.writeVersion) ? BigInt(object.writeVersion) : 0n,
      txnSignature: isSet(object.txnSignature) ? Buffer.from(bytesFromBase64(object.txnSignature)) : undefined,
    };
  },

  toJSON(message: SubscribeUpdateAccountInfo): unknown {
    const obj: any = {};
    if (message.pubkey.length !== 0) {
      obj.pubkey = base64FromBytes(message.pubkey);
    }
    if (message.lamports !== 0n) {
      obj.lamports = message.lamports.toString();
    }
    if (message.owner.length !== 0) {
      obj.owner = base64FromBytes(message.owner);
    }
    if (message.executable !== false) {
      obj.executable = message.executable;
    }
    if (message.rentEpoch !== 0n) {
      obj.rentEpoch = message.rentEpoch.toString();
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    if (message.writeVersion !== 0n) {
      obj.writeVersion = message.writeVersion.toString();
    }
    if (message.txnSignature !== undefined) {
      obj.txnSignature = base64FromBytes(message.txnSignature);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateAccountInfo>, I>>(base?: I): SubscribeUpdateAccountInfo {
    return SubscribeUpdateAccountInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateAccountInfo>, I>>(object: I): SubscribeUpdateAccountInfo {
    const message = createBaseSubscribeUpdateAccountInfo();
    message.pubkey = object.pubkey ?? Buffer.alloc(0);
    message.lamports = object.lamports ?? 0n;
    message.owner = object.owner ?? Buffer.alloc(0);
    message.executable = object.executable ?? false;
    message.rentEpoch = object.rentEpoch ?? 0n;
    message.data = object.data ?? Buffer.alloc(0);
    message.writeVersion = object.writeVersion ?? 0n;
    message.txnSignature = object.txnSignature ?? undefined;
    return message;
  },
};

function createBaseSubscribeUpdatePing(): SubscribeUpdatePing {
  return {};
}

export const SubscribeUpdatePing: MessageFns<SubscribeUpdatePing> = {
  encode(_: SubscribeUpdatePing, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdatePing {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdatePing();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdatePing, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdatePing | SubscribeUpdatePing[]>
      | Iterable<SubscribeUpdatePing | SubscribeUpdatePing[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePing.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdatePing.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdatePing>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdatePing> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePing.decode(p)];
        }
      } else {
        yield* [SubscribeUpdatePing.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeUpdatePing {
    return {};
  },

  toJSON(_: SubscribeUpdatePing): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdatePing>, I>>(base?: I): SubscribeUpdatePing {
    return SubscribeUpdatePing.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdatePing>, I>>(_: I): SubscribeUpdatePing {
    const message = createBaseSubscribeUpdatePing();
    return message;
  },
};

function createBaseSubscribeUpdatePong(): SubscribeUpdatePong {
  return { id: 0 };
}

export const SubscribeUpdatePong: MessageFns<SubscribeUpdatePong> = {
  encode(message: SubscribeUpdatePong, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdatePong {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdatePong();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdatePong, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdatePong | SubscribeUpdatePong[]>
      | Iterable<SubscribeUpdatePong | SubscribeUpdatePong[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePong.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdatePong.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdatePong>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdatePong> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePong.decode(p)];
        }
      } else {
        yield* [SubscribeUpdatePong.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdatePong {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  toJSON(message: SubscribeUpdatePong): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdatePong>, I>>(base?: I): SubscribeUpdatePong {
    return SubscribeUpdatePong.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdatePong>, I>>(object: I): SubscribeUpdatePong {
    const message = createBaseSubscribeUpdatePong();
    message.id = object.id ?? 0;
    return message;
  },
};

function createBaseMessageAddressTableLookup(): MessageAddressTableLookup {
  return { accountKey: Buffer.alloc(0), writableIndexes: Buffer.alloc(0), readonlyIndexes: Buffer.alloc(0) };
}

export const MessageAddressTableLookup: MessageFns<MessageAddressTableLookup> = {
  encode(message: MessageAddressTableLookup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountKey.length !== 0) {
      writer.uint32(10).bytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      writer.uint32(18).bytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      writer.uint32(26).bytes(message.readonlyIndexes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageAddressTableLookup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageAddressTableLookup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountKey = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writableIndexes = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.readonlyIndexes = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageAddressTableLookup, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<MessageAddressTableLookup | MessageAddressTableLookup[]>
      | Iterable<MessageAddressTableLookup | MessageAddressTableLookup[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.encode(p).finish()];
        }
      } else {
        yield* [MessageAddressTableLookup.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageAddressTableLookup>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageAddressTableLookup> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.decode(p)];
        }
      } else {
        yield* [MessageAddressTableLookup.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageAddressTableLookup {
    return {
      accountKey: isSet(object.accountKey) ? Buffer.from(bytesFromBase64(object.accountKey)) : Buffer.alloc(0),
      writableIndexes: isSet(object.writableIndexes)
        ? Buffer.from(bytesFromBase64(object.writableIndexes))
        : Buffer.alloc(0),
      readonlyIndexes: isSet(object.readonlyIndexes)
        ? Buffer.from(bytesFromBase64(object.readonlyIndexes))
        : Buffer.alloc(0),
    };
  },

  toJSON(message: MessageAddressTableLookup): unknown {
    const obj: any = {};
    if (message.accountKey.length !== 0) {
      obj.accountKey = base64FromBytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      obj.writableIndexes = base64FromBytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      obj.readonlyIndexes = base64FromBytes(message.readonlyIndexes);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(base?: I): MessageAddressTableLookup {
    return MessageAddressTableLookup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(object: I): MessageAddressTableLookup {
    const message = createBaseMessageAddressTableLookup();
    message.accountKey = object.accountKey ?? Buffer.alloc(0);
    message.writableIndexes = object.writableIndexes ?? Buffer.alloc(0);
    message.readonlyIndexes = object.readonlyIndexes ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseCompiledInstruction(): CompiledInstruction {
  return { programIdIndex: 0, accounts: Buffer.alloc(0), data: Buffer.alloc(0) };
}

export const CompiledInstruction: MessageFns<CompiledInstruction> = {
  encode(message: CompiledInstruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programIdIndex !== 0) {
      writer.uint32(8).uint32(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      writer.uint32(18).bytes(message.accounts);
    }
    if (message.data.length !== 0) {
      writer.uint32(26).bytes(message.data);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompiledInstruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompiledInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.programIdIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accounts = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<CompiledInstruction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<CompiledInstruction | CompiledInstruction[]>
      | Iterable<CompiledInstruction | CompiledInstruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.encode(p).finish()];
        }
      } else {
        yield* [CompiledInstruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, CompiledInstruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<CompiledInstruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.decode(p)];
        }
      } else {
        yield* [CompiledInstruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): CompiledInstruction {
    return {
      programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
      accounts: isSet(object.accounts) ? Buffer.from(bytesFromBase64(object.accounts)) : Buffer.alloc(0),
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
    };
  },

  toJSON(message: CompiledInstruction): unknown {
    const obj: any = {};
    if (message.programIdIndex !== 0) {
      obj.programIdIndex = Math.round(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      obj.accounts = base64FromBytes(message.accounts);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompiledInstruction>, I>>(base?: I): CompiledInstruction {
    return CompiledInstruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompiledInstruction>, I>>(object: I): CompiledInstruction {
    const message = createBaseCompiledInstruction();
    message.programIdIndex = object.programIdIndex ?? 0;
    message.accounts = object.accounts ?? Buffer.alloc(0);
    message.data = object.data ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseSubscribeParsedRequest(): SubscribeParsedRequest {
  return { ping: undefined };
}

export const SubscribeParsedRequest: MessageFns<SubscribeParsedRequest> = {
  encode(message: SubscribeParsedRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ping !== undefined) {
      SubscribeRequestPing.encode(message.ping, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeParsedRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeParsedRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ping = SubscribeRequestPing.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeParsedRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeParsedRequest | SubscribeParsedRequest[]>
      | Iterable<SubscribeParsedRequest | SubscribeParsedRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeParsedRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeParsedRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeParsedRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeParsedRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeParsedRequest.decode(p)];
        }
      } else {
        yield* [SubscribeParsedRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeParsedRequest {
    return { ping: isSet(object.ping) ? SubscribeRequestPing.fromJSON(object.ping) : undefined };
  },

  toJSON(message: SubscribeParsedRequest): unknown {
    const obj: any = {};
    if (message.ping !== undefined) {
      obj.ping = SubscribeRequestPing.toJSON(message.ping);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeParsedRequest>, I>>(base?: I): SubscribeParsedRequest {
    return SubscribeParsedRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeParsedRequest>, I>>(object: I): SubscribeParsedRequest {
    const message = createBaseSubscribeParsedRequest();
    message.ping = (object.ping !== undefined && object.ping !== null)
      ? SubscribeRequestPing.fromPartial(object.ping)
      : undefined;
    return message;
  },
};

function createBaseInstruction(): Instruction {
  return {
    initialize: undefined,
    setParams: undefined,
    create: undefined,
    buy: undefined,
    sell: undefined,
    withdraw: undefined,
  };
}

export const Instruction: MessageFns<Instruction> = {
  encode(message: Instruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.initialize !== undefined) {
      Initialize.encode(message.initialize, writer.uint32(10).fork()).join();
    }
    if (message.setParams !== undefined) {
      SetParams.encode(message.setParams, writer.uint32(18).fork()).join();
    }
    if (message.create !== undefined) {
      Create.encode(message.create, writer.uint32(26).fork()).join();
    }
    if (message.buy !== undefined) {
      Buy.encode(message.buy, writer.uint32(34).fork()).join();
    }
    if (message.sell !== undefined) {
      Sell.encode(message.sell, writer.uint32(42).fork()).join();
    }
    if (message.withdraw !== undefined) {
      Withdraw.encode(message.withdraw, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Instruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.initialize = Initialize.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.setParams = SetParams.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.create = Create.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.buy = Buy.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.sell = Sell.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.withdraw = Withdraw.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Instruction, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Instruction | Instruction[]> | Iterable<Instruction | Instruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Instruction.encode(p).finish()];
        }
      } else {
        yield* [Instruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Instruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Instruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Instruction.decode(p)];
        }
      } else {
        yield* [Instruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Instruction {
    return {
      initialize: isSet(object.initialize) ? Initialize.fromJSON(object.initialize) : undefined,
      setParams: isSet(object.setParams) ? SetParams.fromJSON(object.setParams) : undefined,
      create: isSet(object.create) ? Create.fromJSON(object.create) : undefined,
      buy: isSet(object.buy) ? Buy.fromJSON(object.buy) : undefined,
      sell: isSet(object.sell) ? Sell.fromJSON(object.sell) : undefined,
      withdraw: isSet(object.withdraw) ? Withdraw.fromJSON(object.withdraw) : undefined,
    };
  },

  toJSON(message: Instruction): unknown {
    const obj: any = {};
    if (message.initialize !== undefined) {
      obj.initialize = Initialize.toJSON(message.initialize);
    }
    if (message.setParams !== undefined) {
      obj.setParams = SetParams.toJSON(message.setParams);
    }
    if (message.create !== undefined) {
      obj.create = Create.toJSON(message.create);
    }
    if (message.buy !== undefined) {
      obj.buy = Buy.toJSON(message.buy);
    }
    if (message.sell !== undefined) {
      obj.sell = Sell.toJSON(message.sell);
    }
    if (message.withdraw !== undefined) {
      obj.withdraw = Withdraw.toJSON(message.withdraw);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Instruction>, I>>(base?: I): Instruction {
    return Instruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Instruction>, I>>(object: I): Instruction {
    const message = createBaseInstruction();
    message.initialize = (object.initialize !== undefined && object.initialize !== null)
      ? Initialize.fromPartial(object.initialize)
      : undefined;
    message.setParams = (object.setParams !== undefined && object.setParams !== null)
      ? SetParams.fromPartial(object.setParams)
      : undefined;
    message.create = (object.create !== undefined && object.create !== null)
      ? Create.fromPartial(object.create)
      : undefined;
    message.buy = (object.buy !== undefined && object.buy !== null) ? Buy.fromPartial(object.buy) : undefined;
    message.sell = (object.sell !== undefined && object.sell !== null) ? Sell.fromPartial(object.sell) : undefined;
    message.withdraw = (object.withdraw !== undefined && object.withdraw !== null)
      ? Withdraw.fromPartial(object.withdraw)
      : undefined;
    return message;
  },
};

function createBaseInitialize(): Initialize {
  return {};
}

export const Initialize: MessageFns<Initialize> = {
  encode(_: Initialize, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Initialize {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitialize();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Initialize, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Initialize | Initialize[]> | Iterable<Initialize | Initialize[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Initialize.encode(p).finish()];
        }
      } else {
        yield* [Initialize.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Initialize>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Initialize> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Initialize.decode(p)];
        }
      } else {
        yield* [Initialize.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): Initialize {
    return {};
  },

  toJSON(_: Initialize): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<Initialize>, I>>(base?: I): Initialize {
    return Initialize.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Initialize>, I>>(_: I): Initialize {
    const message = createBaseInitialize();
    return message;
  },
};

function createBaseSetParams(): SetParams {
  return {
    feeRecipient: Buffer.alloc(0),
    initialVirtualTokenReserves: 0n,
    initialVirtualSolReserves: 0n,
    initialRealTokenReserves: 0n,
    tokenTotalSupply: 0n,
    feeBasisPoints: 0n,
  };
}

export const SetParams: MessageFns<SetParams> = {
  encode(message: SetParams, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.feeRecipient.length !== 0) {
      writer.uint32(10).bytes(message.feeRecipient);
    }
    if (message.initialVirtualTokenReserves !== 0n) {
      if (BigInt.asUintN(64, message.initialVirtualTokenReserves) !== message.initialVirtualTokenReserves) {
        throw new globalThis.Error(
          "value provided for field message.initialVirtualTokenReserves of type uint64 too large",
        );
      }
      writer.uint32(16).uint64(message.initialVirtualTokenReserves);
    }
    if (message.initialVirtualSolReserves !== 0n) {
      if (BigInt.asUintN(64, message.initialVirtualSolReserves) !== message.initialVirtualSolReserves) {
        throw new globalThis.Error(
          "value provided for field message.initialVirtualSolReserves of type uint64 too large",
        );
      }
      writer.uint32(24).uint64(message.initialVirtualSolReserves);
    }
    if (message.initialRealTokenReserves !== 0n) {
      if (BigInt.asUintN(64, message.initialRealTokenReserves) !== message.initialRealTokenReserves) {
        throw new globalThis.Error(
          "value provided for field message.initialRealTokenReserves of type uint64 too large",
        );
      }
      writer.uint32(32).uint64(message.initialRealTokenReserves);
    }
    if (message.tokenTotalSupply !== 0n) {
      if (BigInt.asUintN(64, message.tokenTotalSupply) !== message.tokenTotalSupply) {
        throw new globalThis.Error("value provided for field message.tokenTotalSupply of type uint64 too large");
      }
      writer.uint32(40).uint64(message.tokenTotalSupply);
    }
    if (message.feeBasisPoints !== 0n) {
      if (BigInt.asUintN(64, message.feeBasisPoints) !== message.feeBasisPoints) {
        throw new globalThis.Error("value provided for field message.feeBasisPoints of type uint64 too large");
      }
      writer.uint32(48).uint64(message.feeBasisPoints);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetParams {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetParams();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.feeRecipient = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.initialVirtualTokenReserves = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.initialVirtualSolReserves = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.initialRealTokenReserves = reader.uint64() as bigint;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.tokenTotalSupply = reader.uint64() as bigint;
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.feeBasisPoints = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SetParams, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SetParams | SetParams[]> | Iterable<SetParams | SetParams[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SetParams.encode(p).finish()];
        }
      } else {
        yield* [SetParams.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SetParams>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SetParams> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SetParams.decode(p)];
        }
      } else {
        yield* [SetParams.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SetParams {
    return {
      feeRecipient: isSet(object.feeRecipient) ? Buffer.from(bytesFromBase64(object.feeRecipient)) : Buffer.alloc(0),
      initialVirtualTokenReserves: isSet(object.initialVirtualTokenReserves)
        ? BigInt(object.initialVirtualTokenReserves)
        : 0n,
      initialVirtualSolReserves: isSet(object.initialVirtualSolReserves)
        ? BigInt(object.initialVirtualSolReserves)
        : 0n,
      initialRealTokenReserves: isSet(object.initialRealTokenReserves) ? BigInt(object.initialRealTokenReserves) : 0n,
      tokenTotalSupply: isSet(object.tokenTotalSupply) ? BigInt(object.tokenTotalSupply) : 0n,
      feeBasisPoints: isSet(object.feeBasisPoints) ? BigInt(object.feeBasisPoints) : 0n,
    };
  },

  toJSON(message: SetParams): unknown {
    const obj: any = {};
    if (message.feeRecipient.length !== 0) {
      obj.feeRecipient = base64FromBytes(message.feeRecipient);
    }
    if (message.initialVirtualTokenReserves !== 0n) {
      obj.initialVirtualTokenReserves = message.initialVirtualTokenReserves.toString();
    }
    if (message.initialVirtualSolReserves !== 0n) {
      obj.initialVirtualSolReserves = message.initialVirtualSolReserves.toString();
    }
    if (message.initialRealTokenReserves !== 0n) {
      obj.initialRealTokenReserves = message.initialRealTokenReserves.toString();
    }
    if (message.tokenTotalSupply !== 0n) {
      obj.tokenTotalSupply = message.tokenTotalSupply.toString();
    }
    if (message.feeBasisPoints !== 0n) {
      obj.feeBasisPoints = message.feeBasisPoints.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetParams>, I>>(base?: I): SetParams {
    return SetParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetParams>, I>>(object: I): SetParams {
    const message = createBaseSetParams();
    message.feeRecipient = object.feeRecipient ?? Buffer.alloc(0);
    message.initialVirtualTokenReserves = object.initialVirtualTokenReserves ?? 0n;
    message.initialVirtualSolReserves = object.initialVirtualSolReserves ?? 0n;
    message.initialRealTokenReserves = object.initialRealTokenReserves ?? 0n;
    message.tokenTotalSupply = object.tokenTotalSupply ?? 0n;
    message.feeBasisPoints = object.feeBasisPoints ?? 0n;
    return message;
  },
};

function createBaseCreate(): Create {
  return { name: "", symbol: "", uri: "" };
}

export const Create: MessageFns<Create> = {
  encode(message: Create, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.symbol !== "") {
      writer.uint32(18).string(message.symbol);
    }
    if (message.uri !== "") {
      writer.uint32(26).string(message.uri);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Create {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.uri = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Create, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Create | Create[]> | Iterable<Create | Create[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Create.encode(p).finish()];
        }
      } else {
        yield* [Create.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Create>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Create> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Create.decode(p)];
        }
      } else {
        yield* [Create.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Create {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      symbol: isSet(object.symbol) ? globalThis.String(object.symbol) : "",
      uri: isSet(object.uri) ? globalThis.String(object.uri) : "",
    };
  },

  toJSON(message: Create): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.symbol !== "") {
      obj.symbol = message.symbol;
    }
    if (message.uri !== "") {
      obj.uri = message.uri;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Create>, I>>(base?: I): Create {
    return Create.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Create>, I>>(object: I): Create {
    const message = createBaseCreate();
    message.name = object.name ?? "";
    message.symbol = object.symbol ?? "";
    message.uri = object.uri ?? "";
    return message;
  },
};

function createBaseBuy(): Buy {
  return { amount: 0n, maxSolCost: 0n };
}

export const Buy: MessageFns<Buy> = {
  encode(message: Buy, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0n) {
      if (BigInt.asUintN(64, message.amount) !== message.amount) {
        throw new globalThis.Error("value provided for field message.amount of type uint64 too large");
      }
      writer.uint32(8).uint64(message.amount);
    }
    if (message.maxSolCost !== 0n) {
      if (BigInt.asUintN(64, message.maxSolCost) !== message.maxSolCost) {
        throw new globalThis.Error("value provided for field message.maxSolCost of type uint64 too large");
      }
      writer.uint32(16).uint64(message.maxSolCost);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Buy {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBuy();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.amount = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.maxSolCost = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Buy, Uint8Array>
  async *encodeTransform(source: AsyncIterable<Buy | Buy[]> | Iterable<Buy | Buy[]>): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Buy.encode(p).finish()];
        }
      } else {
        yield* [Buy.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Buy>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Buy> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Buy.decode(p)];
        }
      } else {
        yield* [Buy.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Buy {
    return {
      amount: isSet(object.amount) ? BigInt(object.amount) : 0n,
      maxSolCost: isSet(object.maxSolCost) ? BigInt(object.maxSolCost) : 0n,
    };
  },

  toJSON(message: Buy): unknown {
    const obj: any = {};
    if (message.amount !== 0n) {
      obj.amount = message.amount.toString();
    }
    if (message.maxSolCost !== 0n) {
      obj.maxSolCost = message.maxSolCost.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Buy>, I>>(base?: I): Buy {
    return Buy.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Buy>, I>>(object: I): Buy {
    const message = createBaseBuy();
    message.amount = object.amount ?? 0n;
    message.maxSolCost = object.maxSolCost ?? 0n;
    return message;
  },
};

function createBaseSell(): Sell {
  return { amount: 0n, minSolOutput: 0n };
}

export const Sell: MessageFns<Sell> = {
  encode(message: Sell, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0n) {
      if (BigInt.asUintN(64, message.amount) !== message.amount) {
        throw new globalThis.Error("value provided for field message.amount of type uint64 too large");
      }
      writer.uint32(8).uint64(message.amount);
    }
    if (message.minSolOutput !== 0n) {
      if (BigInt.asUintN(64, message.minSolOutput) !== message.minSolOutput) {
        throw new globalThis.Error("value provided for field message.minSolOutput of type uint64 too large");
      }
      writer.uint32(16).uint64(message.minSolOutput);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Sell {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSell();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.amount = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.minSolOutput = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Sell, Uint8Array>
  async *encodeTransform(source: AsyncIterable<Sell | Sell[]> | Iterable<Sell | Sell[]>): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Sell.encode(p).finish()];
        }
      } else {
        yield* [Sell.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Sell>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Sell> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Sell.decode(p)];
        }
      } else {
        yield* [Sell.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Sell {
    return {
      amount: isSet(object.amount) ? BigInt(object.amount) : 0n,
      minSolOutput: isSet(object.minSolOutput) ? BigInt(object.minSolOutput) : 0n,
    };
  },

  toJSON(message: Sell): unknown {
    const obj: any = {};
    if (message.amount !== 0n) {
      obj.amount = message.amount.toString();
    }
    if (message.minSolOutput !== 0n) {
      obj.minSolOutput = message.minSolOutput.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Sell>, I>>(base?: I): Sell {
    return Sell.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Sell>, I>>(object: I): Sell {
    const message = createBaseSell();
    message.amount = object.amount ?? 0n;
    message.minSolOutput = object.minSolOutput ?? 0n;
    return message;
  },
};

function createBaseWithdraw(): Withdraw {
  return {};
}

export const Withdraw: MessageFns<Withdraw> = {
  encode(_: Withdraw, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Withdraw {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdraw();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Withdraw, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Withdraw | Withdraw[]> | Iterable<Withdraw | Withdraw[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Withdraw.encode(p).finish()];
        }
      } else {
        yield* [Withdraw.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Withdraw>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Withdraw> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Withdraw.decode(p)];
        }
      } else {
        yield* [Withdraw.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): Withdraw {
    return {};
  },

  toJSON(_: Withdraw): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<Withdraw>, I>>(base?: I): Withdraw {
    return Withdraw.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Withdraw>, I>>(_: I): Withdraw {
    const message = createBaseWithdraw();
    return message;
  },
};

function createBaseSubscribeUpdateParsedTransaction(): SubscribeUpdateParsedTransaction {
  return {
    signature: Buffer.alloc(0),
    slot: 0n,
    account: undefined,
    recentBlockhash: Buffer.alloc(0),
    signatures: [],
    instructions: [],
  };
}

export const SubscribeUpdateParsedTransaction: MessageFns<SubscribeUpdateParsedTransaction> = {
  encode(message: SubscribeUpdateParsedTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.signature.length !== 0) {
      writer.uint32(10).bytes(message.signature);
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    if (message.account !== undefined) {
      SubscribeUpdateAccount.encode(message.account, writer.uint32(26).fork()).join();
    }
    if (message.recentBlockhash.length !== 0) {
      writer.uint32(34).bytes(message.recentBlockhash);
    }
    for (const v of message.signatures) {
      writer.uint32(42).bytes(v!);
    }
    for (const v of message.instructions) {
      Instruction.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateParsedTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateParsedTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.signature = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.account = SubscribeUpdateAccount.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.recentBlockhash = Buffer.from(reader.bytes());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.signatures.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.instructions.push(Instruction.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateParsedTransaction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateParsedTransaction | SubscribeUpdateParsedTransaction[]>
      | Iterable<SubscribeUpdateParsedTransaction | SubscribeUpdateParsedTransaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateParsedTransaction.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateParsedTransaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateParsedTransaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateParsedTransaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateParsedTransaction.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateParsedTransaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateParsedTransaction {
    return {
      signature: isSet(object.signature) ? Buffer.from(bytesFromBase64(object.signature)) : Buffer.alloc(0),
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      account: isSet(object.account) ? SubscribeUpdateAccount.fromJSON(object.account) : undefined,
      recentBlockhash: isSet(object.recentBlockhash)
        ? Buffer.from(bytesFromBase64(object.recentBlockhash))
        : Buffer.alloc(0),
      signatures: globalThis.Array.isArray(object?.signatures)
        ? object.signatures.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => Instruction.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SubscribeUpdateParsedTransaction): unknown {
    const obj: any = {};
    if (message.signature.length !== 0) {
      obj.signature = base64FromBytes(message.signature);
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.account !== undefined) {
      obj.account = SubscribeUpdateAccount.toJSON(message.account);
    }
    if (message.recentBlockhash.length !== 0) {
      obj.recentBlockhash = base64FromBytes(message.recentBlockhash);
    }
    if (message.signatures?.length) {
      obj.signatures = message.signatures.map((e) => base64FromBytes(e));
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => Instruction.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateParsedTransaction>, I>>(
    base?: I,
  ): SubscribeUpdateParsedTransaction {
    return SubscribeUpdateParsedTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateParsedTransaction>, I>>(
    object: I,
  ): SubscribeUpdateParsedTransaction {
    const message = createBaseSubscribeUpdateParsedTransaction();
    message.signature = object.signature ?? Buffer.alloc(0);
    message.slot = object.slot ?? 0n;
    message.account = (object.account !== undefined && object.account !== null)
      ? SubscribeUpdateAccount.fromPartial(object.account)
      : undefined;
    message.recentBlockhash = object.recentBlockhash ?? Buffer.alloc(0);
    message.signatures = object.signatures?.map((e) => e) || [];
    message.instructions = object.instructions?.map((e) => Instruction.fromPartial(e)) || [];
    return message;
  },
};

function createBasePingRequest(): PingRequest {
  return { count: 0 };
}

export const PingRequest: MessageFns<PingRequest> = {
  encode(message: PingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PingRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PingRequest | PingRequest[]> | Iterable<PingRequest | PingRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PingRequest.encode(p).finish()];
        }
      } else {
        yield* [PingRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PingRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PingRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PingRequest.decode(p)];
        }
      } else {
        yield* [PingRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PingRequest {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  toJSON(message: PingRequest): unknown {
    const obj: any = {};
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PingRequest>, I>>(base?: I): PingRequest {
    return PingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PingRequest>, I>>(object: I): PingRequest {
    const message = createBasePingRequest();
    message.count = object.count ?? 0;
    return message;
  },
};

function createBasePongResponse(): PongResponse {
  return { count: 0 };
}

export const PongResponse: MessageFns<PongResponse> = {
  encode(message: PongResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PongResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePongResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PongResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PongResponse | PongResponse[]> | Iterable<PongResponse | PongResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PongResponse.encode(p).finish()];
        }
      } else {
        yield* [PongResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PongResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PongResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PongResponse.decode(p)];
        }
      } else {
        yield* [PongResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PongResponse {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  toJSON(message: PongResponse): unknown {
    const obj: any = {};
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PongResponse>, I>>(base?: I): PongResponse {
    return PongResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PongResponse>, I>>(object: I): PongResponse {
    const message = createBasePongResponse();
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseGetVersionRequest(): GetVersionRequest {
  return {};
}

export const GetVersionRequest: MessageFns<GetVersionRequest> = {
  encode(_: GetVersionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetVersionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVersionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetVersionRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<GetVersionRequest | GetVersionRequest[]> | Iterable<GetVersionRequest | GetVersionRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionRequest.encode(p).finish()];
        }
      } else {
        yield* [GetVersionRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetVersionRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetVersionRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionRequest.decode(p)];
        }
      } else {
        yield* [GetVersionRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): GetVersionRequest {
    return {};
  },

  toJSON(_: GetVersionRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetVersionRequest>, I>>(base?: I): GetVersionRequest {
    return GetVersionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVersionRequest>, I>>(_: I): GetVersionRequest {
    const message = createBaseGetVersionRequest();
    return message;
  },
};

function createBaseGetVersionResponse(): GetVersionResponse {
  return { version: "" };
}

export const GetVersionResponse: MessageFns<GetVersionResponse> = {
  encode(message: GetVersionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.version !== "") {
      writer.uint32(10).string(message.version);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetVersionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVersionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.version = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetVersionResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetVersionResponse | GetVersionResponse[]>
      | Iterable<GetVersionResponse | GetVersionResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionResponse.encode(p).finish()];
        }
      } else {
        yield* [GetVersionResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetVersionResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetVersionResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionResponse.decode(p)];
        }
      } else {
        yield* [GetVersionResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetVersionResponse {
    return { version: isSet(object.version) ? globalThis.String(object.version) : "" };
  },

  toJSON(message: GetVersionResponse): unknown {
    const obj: any = {};
    if (message.version !== "") {
      obj.version = message.version;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetVersionResponse>, I>>(base?: I): GetVersionResponse {
    return GetVersionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVersionResponse>, I>>(object: I): GetVersionResponse {
    const message = createBaseGetVersionResponse();
    message.version = object.version ?? "";
    return message;
  },
};

function createBaseGetSlotResponse(): GetSlotResponse {
  return { slot: 0n };
}

export const GetSlotResponse: MessageFns<GetSlotResponse> = {
  encode(message: GetSlotResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetSlotResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSlotResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetSlotResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<GetSlotResponse | GetSlotResponse[]> | Iterable<GetSlotResponse | GetSlotResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetSlotResponse.encode(p).finish()];
        }
      } else {
        yield* [GetSlotResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetSlotResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetSlotResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetSlotResponse.decode(p)];
        }
      } else {
        yield* [GetSlotResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetSlotResponse {
    return { slot: isSet(object.slot) ? BigInt(object.slot) : 0n };
  },

  toJSON(message: GetSlotResponse): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetSlotResponse>, I>>(base?: I): GetSlotResponse {
    return GetSlotResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSlotResponse>, I>>(object: I): GetSlotResponse {
    const message = createBaseGetSlotResponse();
    message.slot = object.slot ?? 0n;
    return message;
  },
};

/** ============= Service Definition ============= */
export type JetstreamService = typeof JetstreamService;
export const JetstreamService = {
  /** Subscribe to data streams with filtering support */
  subscribe: {
    path: "/jetstream.Jetstream/Subscribe",
    requestStream: true,
    responseStream: true,
    requestSerialize: (value: SubscribeRequest) => Buffer.from(SubscribeRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeRequest.decode(value),
    responseSerialize: (value: SubscribeUpdate) => Buffer.from(SubscribeUpdate.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribeUpdate.decode(value),
  },
  /** Subscribe to data streams with filtering support and parsed instructions */
  subscribeParsed: {
    path: "/jetstream.Jetstream/SubscribeParsed",
    requestStream: true,
    responseStream: true,
    requestSerialize: (value: SubscribeParsedRequest) => Buffer.from(SubscribeParsedRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeParsedRequest.decode(value),
    responseSerialize: (value: SubscribeUpdateParsedTransaction) =>
      Buffer.from(SubscribeUpdateParsedTransaction.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribeUpdateParsedTransaction.decode(value),
  },
  /** Basic ping/pong for connection testing */
  ping: {
    path: "/jetstream.Jetstream/Ping",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: PingRequest) => Buffer.from(PingRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => PingRequest.decode(value),
    responseSerialize: (value: PongResponse) => Buffer.from(PongResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => PongResponse.decode(value),
  },
  /** Get information about current state */
  getVersion: {
    path: "/jetstream.Jetstream/GetVersion",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetVersionRequest) => Buffer.from(GetVersionRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetVersionRequest.decode(value),
    responseSerialize: (value: GetVersionResponse) => Buffer.from(GetVersionResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetVersionResponse.decode(value),
  },
} as const;

export interface JetstreamServer extends UntypedServiceImplementation {
  /** Subscribe to data streams with filtering support */
  subscribe: handleBidiStreamingCall<SubscribeRequest, SubscribeUpdate>;
  /** Subscribe to data streams with filtering support and parsed instructions */
  subscribeParsed: handleBidiStreamingCall<SubscribeParsedRequest, SubscribeUpdateParsedTransaction>;
  /** Basic ping/pong for connection testing */
  ping: handleUnaryCall<PingRequest, PongResponse>;
  /** Get information about current state */
  getVersion: handleUnaryCall<GetVersionRequest, GetVersionResponse>;
}

export interface JetstreamClient extends Client {
  /** Subscribe to data streams with filtering support */
  subscribe(): ClientDuplexStream<SubscribeRequest, SubscribeUpdate>;
  subscribe(options: Partial<CallOptions>): ClientDuplexStream<SubscribeRequest, SubscribeUpdate>;
  subscribe(metadata: Metadata, options?: Partial<CallOptions>): ClientDuplexStream<SubscribeRequest, SubscribeUpdate>;
  /** Subscribe to data streams with filtering support and parsed instructions */
  subscribeParsed(): ClientDuplexStream<SubscribeParsedRequest, SubscribeUpdateParsedTransaction>;
  subscribeParsed(
    options: Partial<CallOptions>,
  ): ClientDuplexStream<SubscribeParsedRequest, SubscribeUpdateParsedTransaction>;
  subscribeParsed(
    metadata: Metadata,
    options?: Partial<CallOptions>,
  ): ClientDuplexStream<SubscribeParsedRequest, SubscribeUpdateParsedTransaction>;
  /** Basic ping/pong for connection testing */
  ping(request: PingRequest, callback: (error: ServiceError | null, response: PongResponse) => void): ClientUnaryCall;
  ping(
    request: PingRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: PongResponse) => void,
  ): ClientUnaryCall;
  ping(
    request: PingRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: PongResponse) => void,
  ): ClientUnaryCall;
  /** Get information about current state */
  getVersion(
    request: GetVersionRequest,
    callback: (error: ServiceError | null, response: GetVersionResponse) => void,
  ): ClientUnaryCall;
  getVersion(
    request: GetVersionRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetVersionResponse) => void,
  ): ClientUnaryCall;
  getVersion(
    request: GetVersionRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetVersionResponse) => void,
  ): ClientUnaryCall;
}

export const JetstreamClient = makeGenericClientConstructor(JetstreamService, "jetstream.Jetstream") as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): JetstreamClient;
  service: typeof JetstreamService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
