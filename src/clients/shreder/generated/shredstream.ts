// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: shredstream.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientDuplexStream,
  type ClientOptions,
  type ClientReadableStream,
  type handleBidiStreamingCall,
  type handleServerStreamingCall,
  makeGenericClientConstructor,
  Metadata,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Timestamp } from "./google/protobuf/timestamp";

/** tbd: we may want to add filters here */
export interface SubscribeEntriesRequest {
}

export interface SubscribeTransactionsRequest {
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
}

export interface SubscribeTransactionsRequest_TransactionsEntry {
  key: string;
  value: SubscribeRequestFilterTransactions | undefined;
}

export interface SubscribeTransactionsResponse {
  filters: string[];
  transaction: SubscribeUpdateTransaction | undefined;
  createdAt: Date | undefined;
}

export interface SubscribeUpdateTransaction {
  transaction: Transaction | undefined;
  slot: bigint;
}

export interface SubscribeRequestFilterTransactions {
  accountInclude: string[];
  accountExclude: string[];
  accountRequired: string[];
}

export interface Entry {
  /** the slot that the entry is from */
  slot: bigint;
  /** Serialized bytes of Vec<Entry>: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html */
  entries: Buffer;
}

export interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

export interface CompiledInstruction {
  programIdIndex: number;
  accounts: Buffer;
  data: Buffer;
}

export interface MessageAddressTableLookup {
  accountKey: Buffer;
  writableIndexes: Buffer;
  readonlyIndexes: Buffer;
}

export interface Message {
  header: MessageHeader | undefined;
  accountKeys: Buffer[];
  recentBlockhash: Buffer;
  instructions: CompiledInstruction[];
  versioned: boolean;
  addressTableLookups: MessageAddressTableLookup[];
}

export interface Transaction {
  signatures: Buffer[];
  message: Message | undefined;
}

function createBaseSubscribeEntriesRequest(): SubscribeEntriesRequest {
  return {};
}

export const SubscribeEntriesRequest: MessageFns<SubscribeEntriesRequest> = {
  encode(_: SubscribeEntriesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeEntriesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeEntriesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeEntriesRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeEntriesRequest | SubscribeEntriesRequest[]>
      | Iterable<SubscribeEntriesRequest | SubscribeEntriesRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeEntriesRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeEntriesRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeEntriesRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeEntriesRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeEntriesRequest.decode(p)];
        }
      } else {
        yield* [SubscribeEntriesRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeEntriesRequest {
    return {};
  },

  toJSON(_: SubscribeEntriesRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeEntriesRequest>, I>>(base?: I): SubscribeEntriesRequest {
    return SubscribeEntriesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeEntriesRequest>, I>>(_: I): SubscribeEntriesRequest {
    const message = createBaseSubscribeEntriesRequest();
    return message;
  },
};

function createBaseSubscribeTransactionsRequest(): SubscribeTransactionsRequest {
  return { transactions: {} };
}

export const SubscribeTransactionsRequest: MessageFns<SubscribeTransactionsRequest> = {
  encode(message: SubscribeTransactionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.transactions).forEach(([key, value]) => {
      SubscribeTransactionsRequest_TransactionsEntry.encode({ key: key as any, value }, writer.uint32(26).fork())
        .join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeTransactionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeTransactionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = SubscribeTransactionsRequest_TransactionsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.transactions[entry3.key] = entry3.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeTransactionsRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeTransactionsRequest | SubscribeTransactionsRequest[]>
      | Iterable<SubscribeTransactionsRequest | SubscribeTransactionsRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeTransactionsRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeTransactionsRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeTransactionsRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeTransactionsRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeTransactionsRequest.decode(p)];
        }
      } else {
        yield* [SubscribeTransactionsRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeTransactionsRequest {
    return {
      transactions: isObject(object.transactions)
        ? Object.entries(object.transactions).reduce<{ [key: string]: SubscribeRequestFilterTransactions }>(
          (acc, [key, value]) => {
            acc[key] = SubscribeRequestFilterTransactions.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
    };
  },

  toJSON(message: SubscribeTransactionsRequest): unknown {
    const obj: any = {};
    if (message.transactions) {
      const entries = Object.entries(message.transactions);
      if (entries.length > 0) {
        obj.transactions = {};
        entries.forEach(([k, v]) => {
          obj.transactions[k] = SubscribeRequestFilterTransactions.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeTransactionsRequest>, I>>(base?: I): SubscribeTransactionsRequest {
    return SubscribeTransactionsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeTransactionsRequest>, I>>(object: I): SubscribeTransactionsRequest {
    const message = createBaseSubscribeTransactionsRequest();
    message.transactions = Object.entries(object.transactions ?? {}).reduce<
      { [key: string]: SubscribeRequestFilterTransactions }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = SubscribeRequestFilterTransactions.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseSubscribeTransactionsRequest_TransactionsEntry(): SubscribeTransactionsRequest_TransactionsEntry {
  return { key: "", value: undefined };
}

export const SubscribeTransactionsRequest_TransactionsEntry: MessageFns<
  SubscribeTransactionsRequest_TransactionsEntry
> = {
  encode(
    message: SubscribeTransactionsRequest_TransactionsEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterTransactions.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeTransactionsRequest_TransactionsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeTransactionsRequest_TransactionsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterTransactions.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeTransactionsRequest_TransactionsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeTransactionsRequest_TransactionsEntry | SubscribeTransactionsRequest_TransactionsEntry[]>
      | Iterable<SubscribeTransactionsRequest_TransactionsEntry | SubscribeTransactionsRequest_TransactionsEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeTransactionsRequest_TransactionsEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeTransactionsRequest_TransactionsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeTransactionsRequest_TransactionsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeTransactionsRequest_TransactionsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeTransactionsRequest_TransactionsEntry.decode(p)];
        }
      } else {
        yield* [SubscribeTransactionsRequest_TransactionsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeTransactionsRequest_TransactionsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterTransactions.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeTransactionsRequest_TransactionsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterTransactions.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeTransactionsRequest_TransactionsEntry>, I>>(
    base?: I,
  ): SubscribeTransactionsRequest_TransactionsEntry {
    return SubscribeTransactionsRequest_TransactionsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeTransactionsRequest_TransactionsEntry>, I>>(
    object: I,
  ): SubscribeTransactionsRequest_TransactionsEntry {
    const message = createBaseSubscribeTransactionsRequest_TransactionsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterTransactions.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeTransactionsResponse(): SubscribeTransactionsResponse {
  return { filters: [], transaction: undefined, createdAt: undefined };
}

export const SubscribeTransactionsResponse: MessageFns<SubscribeTransactionsResponse> = {
  encode(message: SubscribeTransactionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.filters) {
      writer.uint32(10).string(v!);
    }
    if (message.transaction !== undefined) {
      SubscribeUpdateTransaction.encode(message.transaction, writer.uint32(34).fork()).join();
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeTransactionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeTransactionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filters.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.transaction = SubscribeUpdateTransaction.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeTransactionsResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeTransactionsResponse | SubscribeTransactionsResponse[]>
      | Iterable<SubscribeTransactionsResponse | SubscribeTransactionsResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeTransactionsResponse.encode(p).finish()];
        }
      } else {
        yield* [SubscribeTransactionsResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeTransactionsResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeTransactionsResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeTransactionsResponse.decode(p)];
        }
      } else {
        yield* [SubscribeTransactionsResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeTransactionsResponse {
    return {
      filters: globalThis.Array.isArray(object?.filters) ? object.filters.map((e: any) => globalThis.String(e)) : [],
      transaction: isSet(object.transaction) ? SubscribeUpdateTransaction.fromJSON(object.transaction) : undefined,
      createdAt: isSet(object.createdAt) ? fromJsonTimestamp(object.createdAt) : undefined,
    };
  },

  toJSON(message: SubscribeTransactionsResponse): unknown {
    const obj: any = {};
    if (message.filters?.length) {
      obj.filters = message.filters;
    }
    if (message.transaction !== undefined) {
      obj.transaction = SubscribeUpdateTransaction.toJSON(message.transaction);
    }
    if (message.createdAt !== undefined) {
      obj.createdAt = message.createdAt.toISOString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeTransactionsResponse>, I>>(base?: I): SubscribeTransactionsResponse {
    return SubscribeTransactionsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeTransactionsResponse>, I>>(
    object: I,
  ): SubscribeTransactionsResponse {
    const message = createBaseSubscribeTransactionsResponse();
    message.filters = object.filters?.map((e) => e) || [];
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? SubscribeUpdateTransaction.fromPartial(object.transaction)
      : undefined;
    message.createdAt = object.createdAt ?? undefined;
    return message;
  },
};

function createBaseSubscribeUpdateTransaction(): SubscribeUpdateTransaction {
  return { transaction: undefined, slot: 0n };
}

export const SubscribeUpdateTransaction: MessageFns<SubscribeUpdateTransaction> = {
  encode(message: SubscribeUpdateTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transaction !== undefined) {
      Transaction.encode(message.transaction, writer.uint32(10).fork()).join();
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transaction = Transaction.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateTransaction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateTransaction | SubscribeUpdateTransaction[]>
      | Iterable<SubscribeUpdateTransaction | SubscribeUpdateTransaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransaction.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateTransaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateTransaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateTransaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransaction.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateTransaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateTransaction {
    return {
      transaction: isSet(object.transaction) ? Transaction.fromJSON(object.transaction) : undefined,
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
    };
  },

  toJSON(message: SubscribeUpdateTransaction): unknown {
    const obj: any = {};
    if (message.transaction !== undefined) {
      obj.transaction = Transaction.toJSON(message.transaction);
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateTransaction>, I>>(base?: I): SubscribeUpdateTransaction {
    return SubscribeUpdateTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateTransaction>, I>>(object: I): SubscribeUpdateTransaction {
    const message = createBaseSubscribeUpdateTransaction();
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? Transaction.fromPartial(object.transaction)
      : undefined;
    message.slot = object.slot ?? 0n;
    return message;
  },
};

function createBaseSubscribeRequestFilterTransactions(): SubscribeRequestFilterTransactions {
  return { accountInclude: [], accountExclude: [], accountRequired: [] };
}

export const SubscribeRequestFilterTransactions: MessageFns<SubscribeRequestFilterTransactions> = {
  encode(message: SubscribeRequestFilterTransactions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accountInclude) {
      writer.uint32(26).string(v!);
    }
    for (const v of message.accountExclude) {
      writer.uint32(34).string(v!);
    }
    for (const v of message.accountRequired) {
      writer.uint32(50).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterTransactions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterTransactions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accountInclude.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.accountExclude.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.accountRequired.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterTransactions, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterTransactions | SubscribeRequestFilterTransactions[]>
      | Iterable<SubscribeRequestFilterTransactions | SubscribeRequestFilterTransactions[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterTransactions.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterTransactions.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterTransactions>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterTransactions> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterTransactions.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterTransactions.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterTransactions {
    return {
      accountInclude: globalThis.Array.isArray(object?.accountInclude)
        ? object.accountInclude.map((e: any) => globalThis.String(e))
        : [],
      accountExclude: globalThis.Array.isArray(object?.accountExclude)
        ? object.accountExclude.map((e: any) => globalThis.String(e))
        : [],
      accountRequired: globalThis.Array.isArray(object?.accountRequired)
        ? object.accountRequired.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: SubscribeRequestFilterTransactions): unknown {
    const obj: any = {};
    if (message.accountInclude?.length) {
      obj.accountInclude = message.accountInclude;
    }
    if (message.accountExclude?.length) {
      obj.accountExclude = message.accountExclude;
    }
    if (message.accountRequired?.length) {
      obj.accountRequired = message.accountRequired;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterTransactions>, I>>(
    base?: I,
  ): SubscribeRequestFilterTransactions {
    return SubscribeRequestFilterTransactions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterTransactions>, I>>(
    object: I,
  ): SubscribeRequestFilterTransactions {
    const message = createBaseSubscribeRequestFilterTransactions();
    message.accountInclude = object.accountInclude?.map((e) => e) || [];
    message.accountExclude = object.accountExclude?.map((e) => e) || [];
    message.accountRequired = object.accountRequired?.map((e) => e) || [];
    return message;
  },
};

function createBaseEntry(): Entry {
  return { slot: 0n, entries: Buffer.alloc(0) };
}

export const Entry: MessageFns<Entry> = {
  encode(message: Entry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.entries.length !== 0) {
      writer.uint32(18).bytes(message.entries);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Entry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.entries = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Entry, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Entry | Entry[]> | Iterable<Entry | Entry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Entry.encode(p).finish()];
        }
      } else {
        yield* [Entry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Entry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Entry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Entry.decode(p)];
        }
      } else {
        yield* [Entry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Entry {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      entries: isSet(object.entries) ? Buffer.from(bytesFromBase64(object.entries)) : Buffer.alloc(0),
    };
  },

  toJSON(message: Entry): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.entries.length !== 0) {
      obj.entries = base64FromBytes(message.entries);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Entry>, I>>(base?: I): Entry {
    return Entry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Entry>, I>>(object: I): Entry {
    const message = createBaseEntry();
    message.slot = object.slot ?? 0n;
    message.entries = object.entries ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseMessageHeader(): MessageHeader {
  return { numRequiredSignatures: 0, numReadonlySignedAccounts: 0, numReadonlyUnsignedAccounts: 0 };
}

export const MessageHeader: MessageFns<MessageHeader> = {
  encode(message: MessageHeader, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.numRequiredSignatures !== 0) {
      writer.uint32(8).uint32(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      writer.uint32(16).uint32(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      writer.uint32(24).uint32(message.numReadonlyUnsignedAccounts);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageHeader {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageHeader();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.numRequiredSignatures = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.numReadonlySignedAccounts = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.numReadonlyUnsignedAccounts = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageHeader, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<MessageHeader | MessageHeader[]> | Iterable<MessageHeader | MessageHeader[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.encode(p).finish()];
        }
      } else {
        yield* [MessageHeader.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageHeader>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageHeader> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.decode(p)];
        }
      } else {
        yield* [MessageHeader.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageHeader {
    return {
      numRequiredSignatures: isSet(object.numRequiredSignatures) ? globalThis.Number(object.numRequiredSignatures) : 0,
      numReadonlySignedAccounts: isSet(object.numReadonlySignedAccounts)
        ? globalThis.Number(object.numReadonlySignedAccounts)
        : 0,
      numReadonlyUnsignedAccounts: isSet(object.numReadonlyUnsignedAccounts)
        ? globalThis.Number(object.numReadonlyUnsignedAccounts)
        : 0,
    };
  },

  toJSON(message: MessageHeader): unknown {
    const obj: any = {};
    if (message.numRequiredSignatures !== 0) {
      obj.numRequiredSignatures = Math.round(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      obj.numReadonlySignedAccounts = Math.round(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      obj.numReadonlyUnsignedAccounts = Math.round(message.numReadonlyUnsignedAccounts);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageHeader>, I>>(base?: I): MessageHeader {
    return MessageHeader.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageHeader>, I>>(object: I): MessageHeader {
    const message = createBaseMessageHeader();
    message.numRequiredSignatures = object.numRequiredSignatures ?? 0;
    message.numReadonlySignedAccounts = object.numReadonlySignedAccounts ?? 0;
    message.numReadonlyUnsignedAccounts = object.numReadonlyUnsignedAccounts ?? 0;
    return message;
  },
};

function createBaseCompiledInstruction(): CompiledInstruction {
  return { programIdIndex: 0, accounts: Buffer.alloc(0), data: Buffer.alloc(0) };
}

export const CompiledInstruction: MessageFns<CompiledInstruction> = {
  encode(message: CompiledInstruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programIdIndex !== 0) {
      writer.uint32(8).uint32(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      writer.uint32(18).bytes(message.accounts);
    }
    if (message.data.length !== 0) {
      writer.uint32(26).bytes(message.data);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompiledInstruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompiledInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.programIdIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accounts = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<CompiledInstruction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<CompiledInstruction | CompiledInstruction[]>
      | Iterable<CompiledInstruction | CompiledInstruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.encode(p).finish()];
        }
      } else {
        yield* [CompiledInstruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, CompiledInstruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<CompiledInstruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.decode(p)];
        }
      } else {
        yield* [CompiledInstruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): CompiledInstruction {
    return {
      programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
      accounts: isSet(object.accounts) ? Buffer.from(bytesFromBase64(object.accounts)) : Buffer.alloc(0),
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
    };
  },

  toJSON(message: CompiledInstruction): unknown {
    const obj: any = {};
    if (message.programIdIndex !== 0) {
      obj.programIdIndex = Math.round(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      obj.accounts = base64FromBytes(message.accounts);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompiledInstruction>, I>>(base?: I): CompiledInstruction {
    return CompiledInstruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompiledInstruction>, I>>(object: I): CompiledInstruction {
    const message = createBaseCompiledInstruction();
    message.programIdIndex = object.programIdIndex ?? 0;
    message.accounts = object.accounts ?? Buffer.alloc(0);
    message.data = object.data ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseMessageAddressTableLookup(): MessageAddressTableLookup {
  return { accountKey: Buffer.alloc(0), writableIndexes: Buffer.alloc(0), readonlyIndexes: Buffer.alloc(0) };
}

export const MessageAddressTableLookup: MessageFns<MessageAddressTableLookup> = {
  encode(message: MessageAddressTableLookup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountKey.length !== 0) {
      writer.uint32(10).bytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      writer.uint32(18).bytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      writer.uint32(26).bytes(message.readonlyIndexes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageAddressTableLookup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageAddressTableLookup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountKey = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writableIndexes = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.readonlyIndexes = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageAddressTableLookup, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<MessageAddressTableLookup | MessageAddressTableLookup[]>
      | Iterable<MessageAddressTableLookup | MessageAddressTableLookup[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.encode(p).finish()];
        }
      } else {
        yield* [MessageAddressTableLookup.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageAddressTableLookup>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageAddressTableLookup> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.decode(p)];
        }
      } else {
        yield* [MessageAddressTableLookup.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageAddressTableLookup {
    return {
      accountKey: isSet(object.accountKey) ? Buffer.from(bytesFromBase64(object.accountKey)) : Buffer.alloc(0),
      writableIndexes: isSet(object.writableIndexes)
        ? Buffer.from(bytesFromBase64(object.writableIndexes))
        : Buffer.alloc(0),
      readonlyIndexes: isSet(object.readonlyIndexes)
        ? Buffer.from(bytesFromBase64(object.readonlyIndexes))
        : Buffer.alloc(0),
    };
  },

  toJSON(message: MessageAddressTableLookup): unknown {
    const obj: any = {};
    if (message.accountKey.length !== 0) {
      obj.accountKey = base64FromBytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      obj.writableIndexes = base64FromBytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      obj.readonlyIndexes = base64FromBytes(message.readonlyIndexes);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(base?: I): MessageAddressTableLookup {
    return MessageAddressTableLookup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(object: I): MessageAddressTableLookup {
    const message = createBaseMessageAddressTableLookup();
    message.accountKey = object.accountKey ?? Buffer.alloc(0);
    message.writableIndexes = object.writableIndexes ?? Buffer.alloc(0);
    message.readonlyIndexes = object.readonlyIndexes ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseMessage(): Message {
  return {
    header: undefined,
    accountKeys: [],
    recentBlockhash: Buffer.alloc(0),
    instructions: [],
    versioned: false,
    addressTableLookups: [],
  };
}

export const Message: MessageFns<Message> = {
  encode(message: Message, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      MessageHeader.encode(message.header, writer.uint32(10).fork()).join();
    }
    for (const v of message.accountKeys) {
      writer.uint32(18).bytes(v!);
    }
    if (message.recentBlockhash.length !== 0) {
      writer.uint32(26).bytes(message.recentBlockhash);
    }
    for (const v of message.instructions) {
      CompiledInstruction.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.versioned !== false) {
      writer.uint32(40).bool(message.versioned);
    }
    for (const v of message.addressTableLookups) {
      MessageAddressTableLookup.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Message {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = MessageHeader.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountKeys.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.recentBlockhash = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.instructions.push(CompiledInstruction.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.versioned = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.addressTableLookups.push(MessageAddressTableLookup.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Message, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Message | Message[]> | Iterable<Message | Message[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Message.encode(p).finish()];
        }
      } else {
        yield* [Message.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Message>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Message> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Message.decode(p)];
        }
      } else {
        yield* [Message.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Message {
    return {
      header: isSet(object.header) ? MessageHeader.fromJSON(object.header) : undefined,
      accountKeys: globalThis.Array.isArray(object?.accountKeys)
        ? object.accountKeys.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      recentBlockhash: isSet(object.recentBlockhash)
        ? Buffer.from(bytesFromBase64(object.recentBlockhash))
        : Buffer.alloc(0),
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => CompiledInstruction.fromJSON(e))
        : [],
      versioned: isSet(object.versioned) ? globalThis.Boolean(object.versioned) : false,
      addressTableLookups: globalThis.Array.isArray(object?.addressTableLookups)
        ? object.addressTableLookups.map((e: any) => MessageAddressTableLookup.fromJSON(e))
        : [],
    };
  },

  toJSON(message: Message): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = MessageHeader.toJSON(message.header);
    }
    if (message.accountKeys?.length) {
      obj.accountKeys = message.accountKeys.map((e) => base64FromBytes(e));
    }
    if (message.recentBlockhash.length !== 0) {
      obj.recentBlockhash = base64FromBytes(message.recentBlockhash);
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => CompiledInstruction.toJSON(e));
    }
    if (message.versioned !== false) {
      obj.versioned = message.versioned;
    }
    if (message.addressTableLookups?.length) {
      obj.addressTableLookups = message.addressTableLookups.map((e) => MessageAddressTableLookup.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Message>, I>>(base?: I): Message {
    return Message.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Message>, I>>(object: I): Message {
    const message = createBaseMessage();
    message.header = (object.header !== undefined && object.header !== null)
      ? MessageHeader.fromPartial(object.header)
      : undefined;
    message.accountKeys = object.accountKeys?.map((e) => e) || [];
    message.recentBlockhash = object.recentBlockhash ?? Buffer.alloc(0);
    message.instructions = object.instructions?.map((e) => CompiledInstruction.fromPartial(e)) || [];
    message.versioned = object.versioned ?? false;
    message.addressTableLookups = object.addressTableLookups?.map((e) => MessageAddressTableLookup.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseTransaction(): Transaction {
  return { signatures: [], message: undefined };
}

export const Transaction: MessageFns<Transaction> = {
  encode(message: Transaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.signatures) {
      writer.uint32(10).bytes(v!);
    }
    if (message.message !== undefined) {
      Message.encode(message.message, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Transaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.signatures.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = Message.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Transaction, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Transaction | Transaction[]> | Iterable<Transaction | Transaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Transaction.encode(p).finish()];
        }
      } else {
        yield* [Transaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Transaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Transaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Transaction.decode(p)];
        }
      } else {
        yield* [Transaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Transaction {
    return {
      signatures: globalThis.Array.isArray(object?.signatures)
        ? object.signatures.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      message: isSet(object.message) ? Message.fromJSON(object.message) : undefined,
    };
  },

  toJSON(message: Transaction): unknown {
    const obj: any = {};
    if (message.signatures?.length) {
      obj.signatures = message.signatures.map((e) => base64FromBytes(e));
    }
    if (message.message !== undefined) {
      obj.message = Message.toJSON(message.message);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Transaction>, I>>(base?: I): Transaction {
    return Transaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Transaction>, I>>(object: I): Transaction {
    const message = createBaseTransaction();
    message.signatures = object.signatures?.map((e) => e) || [];
    message.message = (object.message !== undefined && object.message !== null)
      ? Message.fromPartial(object.message)
      : undefined;
    return message;
  },
};

export type ShrederServiceService = typeof ShrederServiceService;
export const ShrederServiceService = {
  subscribeEntries: {
    path: "/shredstream.ShrederService/SubscribeEntries",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribeEntriesRequest) => Buffer.from(SubscribeEntriesRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeEntriesRequest.decode(value),
    responseSerialize: (value: Entry) => Buffer.from(Entry.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Entry.decode(value),
  },
  subscribeTransactions: {
    path: "/shredstream.ShrederService/SubscribeTransactions",
    requestStream: true,
    responseStream: true,
    requestSerialize: (value: SubscribeTransactionsRequest) =>
      Buffer.from(SubscribeTransactionsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeTransactionsRequest.decode(value),
    responseSerialize: (value: SubscribeTransactionsResponse) =>
      Buffer.from(SubscribeTransactionsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribeTransactionsResponse.decode(value),
  },
} as const;

export interface ShrederServiceServer extends UntypedServiceImplementation {
  subscribeEntries: handleServerStreamingCall<SubscribeEntriesRequest, Entry>;
  subscribeTransactions: handleBidiStreamingCall<SubscribeTransactionsRequest, SubscribeTransactionsResponse>;
}

export interface ShrederServiceClient extends Client {
  subscribeEntries(request: SubscribeEntriesRequest, options?: Partial<CallOptions>): ClientReadableStream<Entry>;
  subscribeEntries(
    request: SubscribeEntriesRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<Entry>;
  subscribeTransactions(): ClientDuplexStream<SubscribeTransactionsRequest, SubscribeTransactionsResponse>;
  subscribeTransactions(
    options: Partial<CallOptions>,
  ): ClientDuplexStream<SubscribeTransactionsRequest, SubscribeTransactionsResponse>;
  subscribeTransactions(
    metadata: Metadata,
    options?: Partial<CallOptions>,
  ): ClientDuplexStream<SubscribeTransactionsRequest, SubscribeTransactionsResponse>;
}

export const ShrederServiceClient = makeGenericClientConstructor(
  ShrederServiceService,
  "shredstream.ShrederService",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): ShrederServiceClient;
  service: typeof ShrederServiceService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
