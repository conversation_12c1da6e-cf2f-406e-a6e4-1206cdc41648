{"name": "@kdt-sol/solana-grpc-client", "type": "module", "version": "0.1.1", "packageManager": "pnpm@10.11.0", "description": "Typescript client implements for multiple Solana GRPC services (YellowStone Geyser GRPC, Orbit JetStream,...)", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kdt-sol/solana-grpc-client", "repository": "github:kdt-sol/solana-grpc-client", "bugs": {"email": "<EMAIL>", "url": "https://github.com/kdt-sol/solana-grpc-client/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./jetstream": {"types": "./dist/types/clients/jetstream/index.d.ts", "require": "./dist/clients/jetstream/index.cjs", "default": "./dist/clients/jetstream/index.js"}, "./clients/jetstream": {"types": "./dist/types/clients/jetstream/index.d.ts", "require": "./dist/clients/jetstream/index.cjs", "default": "./dist/clients/jetstream/index.js"}, "./yellowstone": {"types": "./dist/types/clients/yellowstone/index.d.ts", "require": "./dist/clients/yellowstone/index.cjs", "default": "./dist/clients/yellowstone/index.js"}, "./clients/yellowstone": {"types": "./dist/types/clients/yellowstone/index.d.ts", "require": "./dist/clients/yellowstone/index.cjs", "default": "./dist/clients/yellowstone/index.js"}, "./thor-streamer": {"types": "./dist/types/clients/thor-streamer/index.d.ts", "require": "./dist/clients/thor-streamer/index.cjs", "default": "./dist/clients/thor-streamer/index.js"}, "./clients/thor-streamer": {"types": "./dist/types/clients/thor-streamer/index.d.ts", "require": "./dist/clients/thor-streamer/index.cjs", "default": "./dist/clients/thor-streamer/index.js"}, "./shreder": {"types": "./dist/types/clients/shreder/index.d.ts", "require": "./dist/clients/shreder/index.cjs", "default": "./dist/clients/shreder/index.js"}, "./clients/shreder": {"types": "./dist/types/clients/shreder/index.d.ts", "require": "./dist/clients/shreder/index.cjs", "default": "./dist/clients/shreder/index.js"}}, "main": "dist/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src"], "publishConfig": {"access": "public"}, "scripts": {"build": "pnpm run proto:generate && rimraf dist && tsup && tsc --project ./tsconfig.build.json", "proto:generate": "pnpm run proto:generate:yellowstone && pnpm run proto:generate:jetstream && pnpm run proto:generate:thor-streamer && pnpm run proto:generate:shreder", "proto:generate:yellowstone": "rm -rf ./src/clients/yellowstone/generated && mkdir -p ./src/clients/yellowstone/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/yellowstone/generated --proto_path=./protos/yellowstone/yellowstone-grpc-proto/proto ./protos/yellowstone/yellowstone-grpc-proto/proto/*.proto", "proto:generate:jetstream": "rm -rf ./src/clients/jetstream/generated && mkdir -p ./src/clients/jetstream/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/jetstream/generated --proto_path=./protos/jetstream/jetstream_protos/protos ./protos/jetstream/jetstream_protos/protos/*.proto", "proto:generate:thor-streamer": "rm -rf ./src/clients/thor-streamer/generated && mkdir -p ./src/clients/thor-streamer/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/thor-streamer/generated --proto_path=./protos/thor-streamer/proto ./protos/thor-streamer/proto/*.proto", "proto:generate:shreder": "rm -rf ./src/clients/shreder/generated && mkdir -p ./src/clients/shreder/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/shreder/generated --proto_path=./protos/shreder/proto ./protos/shreder/proto/*.proto", "release": "tsx scripts/release.ts && changelogen gh release && pnpm publish", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks", "prepublishOnly": "pnpm build"}, "dependencies": {"@bufbuild/protobuf": "^2.4.0", "@grpc/grpc-js": "^1.13.4", "@kdt310722/utils": "^0.0.17"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt310722/eslint-config": "^0.2.0", "@kdt310722/tsconfig": "^1.0.0", "@swc/core": "^1.11.29", "@types/node": "^22.15.21", "changelogen": "^0.6.1", "eslint": "^9.27.0", "execa": "^9.5.3", "lint-staged": "^16.0.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.13.0", "ts-proto": "^2.7.0", "tsup": "^8.5.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "pnpm": {"ignoredBuiltDependencies": ["@kdt310722/eslint-config", "@kdt310722/utils"], "onlyBuiltDependencies": ["@swc/core", "esbuild", "protobufjs", "simple-git-hooks", "unrs-resolver"]}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}