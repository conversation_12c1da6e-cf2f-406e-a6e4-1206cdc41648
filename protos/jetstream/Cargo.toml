[workspace]
members = ["jetstream_protos", "jetstream_client"]
resolver = "2"

[workspace.package]
version = "0.2.5"
description = "Fastest path to receive transactions from Solana. More info at https://docs.orbitflare.com/data-streaming/jetstream"
authors = ["OrbitFlare Team <<EMAIL>>"]
homepage = "https://orbitflare.com/"
edition = "2021"

[profile.release]
# thin has minimal overhead vs none (default): https://blog.llvm.org/2016/06/thinlto-scalable-and-incremental-lto.html
lto = "thin"

[workspace.dependencies]
anyhow = "1.0.98"
tokio = { version = "1", features = ["full"] }
tokio-stream = { version = "0.1.17", features = ["sync"] }
prost = "0.13.1"
prost-types = "0.13.3"
tonic = "0.12.3"
solana-sdk = "=2.2.1"
bs58 = "0.5.1"
log = "0.4"
env_logger = "0.11"
clap = { version = "4", features = ["derive", "env"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1"
futures = "0.3.24"
jetstream_protos = { path = "./jetstream_protos" }
borsh = "1.5.5"
borsh-derive = "1.5.3"

[workspace.build-dependencies]
protobuf-src = "1.1.0"
tonic-build = "0.12.3"
anyhow = "1.0.98"
