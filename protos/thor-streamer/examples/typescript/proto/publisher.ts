/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 3.12.4
 * source: publisher.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as dependency_1 from "./google/protobuf/empty";
import * as pb_1 from "google-protobuf";
import * as grpc_1 from "@grpc/grpc-js";
export namespace publisher {
    export class StreamResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            data?: Uint8Array;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("data" in data && data.data != undefined) {
                    this.data = data.data;
                }
            }
        }
        get data() {
            return pb_1.Message.getFieldWithDefault(this, 1, new Uint8Array(0)) as Uint8Array;
        }
        set data(value: Uint8Array) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            data?: Uint8Array;
        }): StreamResponse {
            const message = new StreamResponse({});
            if (data.data != null) {
                message.data = data.data;
            }
            return message;
        }
        toObject() {
            const data: {
                data?: Uint8Array;
            } = {};
            if (this.data != null) {
                data.data = this.data;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.data.length)
                writer.writeBytes(1, this.data);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): StreamResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new StreamResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.data = reader.readBytes();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): StreamResponse {
            return StreamResponse.deserialize(bytes);
        }
    }
    export class SubscribeWalletRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            wallet_address?: string[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("wallet_address" in data && data.wallet_address != undefined) {
                    this.wallet_address = data.wallet_address;
                }
            }
        }
        get wallet_address() {
            return pb_1.Message.getFieldWithDefault(this, 1, []) as string[];
        }
        set wallet_address(value: string[]) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            wallet_address?: string[];
        }): SubscribeWalletRequest {
            const message = new SubscribeWalletRequest({});
            if (data.wallet_address != null) {
                message.wallet_address = data.wallet_address;
            }
            return message;
        }
        toObject() {
            const data: {
                wallet_address?: string[];
            } = {};
            if (this.wallet_address != null) {
                data.wallet_address = this.wallet_address;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.wallet_address.length)
                writer.writeRepeatedString(1, this.wallet_address);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SubscribeWalletRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SubscribeWalletRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        pb_1.Message.addToRepeatedField(message, 1, reader.readString());
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SubscribeWalletRequest {
            return SubscribeWalletRequest.deserialize(bytes);
        }
    }
    interface GrpcUnaryServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
    }
    interface GrpcStreamServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
        (message: P, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
    }
    interface GrpWritableServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
    }
    interface GrpcChunkServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
        (options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
    }
    interface GrpcPromiseServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): Promise<R>;
        (message: P, options?: grpc_1.CallOptions): Promise<R>;
    }
    export abstract class UnimplementedEventPublisherService {
        static definition = {
            SubscribeToTransactions: {
                path: "/publisher.EventPublisher/SubscribeToTransactions",
                requestStream: false,
                responseStream: true,
                requestSerialize: (message: dependency_1.google.protobuf.Empty) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => dependency_1.google.protobuf.Empty.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: StreamResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => StreamResponse.deserialize(new Uint8Array(bytes))
            },
            SubscribeToAccountUpdates: {
                path: "/publisher.EventPublisher/SubscribeToAccountUpdates",
                requestStream: false,
                responseStream: true,
                requestSerialize: (message: dependency_1.google.protobuf.Empty) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => dependency_1.google.protobuf.Empty.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: StreamResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => StreamResponse.deserialize(new Uint8Array(bytes))
            },
            SubscribeToSlotStatus: {
                path: "/publisher.EventPublisher/SubscribeToSlotStatus",
                requestStream: false,
                responseStream: true,
                requestSerialize: (message: dependency_1.google.protobuf.Empty) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => dependency_1.google.protobuf.Empty.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: StreamResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => StreamResponse.deserialize(new Uint8Array(bytes))
            },
            SubscribeToWalletTransactions: {
                path: "/publisher.EventPublisher/SubscribeToWalletTransactions",
                requestStream: false,
                responseStream: true,
                requestSerialize: (message: SubscribeWalletRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => SubscribeWalletRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: StreamResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => StreamResponse.deserialize(new Uint8Array(bytes))
            }
        };
        [method: string]: grpc_1.UntypedHandleCall;
        abstract SubscribeToTransactions(call: grpc_1.ServerWritableStream<dependency_1.google.protobuf.Empty, StreamResponse>): void;
        abstract SubscribeToAccountUpdates(call: grpc_1.ServerWritableStream<dependency_1.google.protobuf.Empty, StreamResponse>): void;
        abstract SubscribeToSlotStatus(call: grpc_1.ServerWritableStream<dependency_1.google.protobuf.Empty, StreamResponse>): void;
        abstract SubscribeToWalletTransactions(call: grpc_1.ServerWritableStream<SubscribeWalletRequest, StreamResponse>): void;
    }
    export class EventPublisherClient extends grpc_1.makeGenericClientConstructor(UnimplementedEventPublisherService.definition, "EventPublisher", {}) {
        constructor(address: string, credentials: grpc_1.ChannelCredentials, options?: Partial<grpc_1.ChannelOptions>) {
            super(address, credentials, options);
        }
        SubscribeToTransactions: GrpcStreamServiceInterface<dependency_1.google.protobuf.Empty, StreamResponse> = (message: dependency_1.google.protobuf.Empty, metadata?: grpc_1.Metadata | grpc_1.CallOptions, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<StreamResponse> => {
            return super.SubscribeToTransactions(message, metadata, options);
        };
        SubscribeToAccountUpdates: GrpcStreamServiceInterface<dependency_1.google.protobuf.Empty, StreamResponse> = (message: dependency_1.google.protobuf.Empty, metadata?: grpc_1.Metadata | grpc_1.CallOptions, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<StreamResponse> => {
            return super.SubscribeToAccountUpdates(message, metadata, options);
        };
        SubscribeToSlotStatus: GrpcStreamServiceInterface<dependency_1.google.protobuf.Empty, StreamResponse> = (message: dependency_1.google.protobuf.Empty, metadata?: grpc_1.Metadata | grpc_1.CallOptions, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<StreamResponse> => {
            return super.SubscribeToSlotStatus(message, metadata, options);
        };
        SubscribeToWalletTransactions: GrpcStreamServiceInterface<SubscribeWalletRequest, StreamResponse> = (message: SubscribeWalletRequest, metadata?: grpc_1.Metadata | grpc_1.CallOptions, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<StreamResponse> => {
            return super.SubscribeToWalletTransactions(message, metadata, options);
        };
    }
}
