/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 3.12.4
 * source: events.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
export namespace thor_streamer.types {
    export enum SlotStatus {
        PROCESSED = 0,
        CONFIRMED = 1,
        ROOTED = 2
    }
    export enum StreamType {
        STREAM_TYPE_UNSPECIFIED = 0,
        STREAM_TYPE_FILTERED = 1,
        STREAM_TYPE_WALLET = 2
    }
    export class UpdateAccountEvent extends pb_1.Message {
        #one_of_decls: number[][] = [[9]];
        constructor(data?: any[] | ({
            slot?: number;
            lamports?: number;
            rent_epoch?: number;
            write_version?: number;
            executable?: boolean;
            pubkey?: Uint8Array;
            owner?: Uint8Array;
            data?: Uint8Array;
        } & (({
            txn_signature?: Uint8Array;
        })))) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("slot" in data && data.slot != undefined) {
                    this.slot = data.slot;
                }
                if ("lamports" in data && data.lamports != undefined) {
                    this.lamports = data.lamports;
                }
                if ("rent_epoch" in data && data.rent_epoch != undefined) {
                    this.rent_epoch = data.rent_epoch;
                }
                if ("write_version" in data && data.write_version != undefined) {
                    this.write_version = data.write_version;
                }
                if ("executable" in data && data.executable != undefined) {
                    this.executable = data.executable;
                }
                if ("pubkey" in data && data.pubkey != undefined) {
                    this.pubkey = data.pubkey;
                }
                if ("owner" in data && data.owner != undefined) {
                    this.owner = data.owner;
                }
                if ("data" in data && data.data != undefined) {
                    this.data = data.data;
                }
                if ("txn_signature" in data && data.txn_signature != undefined) {
                    this.txn_signature = data.txn_signature;
                }
            }
        }
        get slot() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set slot(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get lamports() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set lamports(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get rent_epoch() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set rent_epoch(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get write_version() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set write_version(value: number) {
            pb_1.Message.setField(this, 4, value);
        }
        get executable() {
            return pb_1.Message.getFieldWithDefault(this, 5, false) as boolean;
        }
        set executable(value: boolean) {
            pb_1.Message.setField(this, 5, value);
        }
        get pubkey() {
            return pb_1.Message.getFieldWithDefault(this, 6, new Uint8Array(0)) as Uint8Array;
        }
        set pubkey(value: Uint8Array) {
            pb_1.Message.setField(this, 6, value);
        }
        get owner() {
            return pb_1.Message.getFieldWithDefault(this, 7, new Uint8Array(0)) as Uint8Array;
        }
        set owner(value: Uint8Array) {
            pb_1.Message.setField(this, 7, value);
        }
        get data() {
            return pb_1.Message.getFieldWithDefault(this, 8, new Uint8Array(0)) as Uint8Array;
        }
        set data(value: Uint8Array) {
            pb_1.Message.setField(this, 8, value);
        }
        get txn_signature() {
            return pb_1.Message.getFieldWithDefault(this, 9, new Uint8Array(0)) as Uint8Array;
        }
        set txn_signature(value: Uint8Array) {
            pb_1.Message.setOneofField(this, 9, this.#one_of_decls[0], value);
        }
        get has_txn_signature() {
            return pb_1.Message.getField(this, 9) != null;
        }
        get _txn_signature() {
            const cases: {
                [index: number]: "none" | "txn_signature";
            } = {
                0: "none",
                9: "txn_signature"
            };
            return cases[pb_1.Message.computeOneofCase(this, [9])];
        }
        static fromObject(data: {
            slot?: number;
            lamports?: number;
            rent_epoch?: number;
            write_version?: number;
            executable?: boolean;
            pubkey?: Uint8Array;
            owner?: Uint8Array;
            data?: Uint8Array;
            txn_signature?: Uint8Array;
        }): UpdateAccountEvent {
            const message = new UpdateAccountEvent({});
            if (data.slot != null) {
                message.slot = data.slot;
            }
            if (data.lamports != null) {
                message.lamports = data.lamports;
            }
            if (data.rent_epoch != null) {
                message.rent_epoch = data.rent_epoch;
            }
            if (data.write_version != null) {
                message.write_version = data.write_version;
            }
            if (data.executable != null) {
                message.executable = data.executable;
            }
            if (data.pubkey != null) {
                message.pubkey = data.pubkey;
            }
            if (data.owner != null) {
                message.owner = data.owner;
            }
            if (data.data != null) {
                message.data = data.data;
            }
            if (data.txn_signature != null) {
                message.txn_signature = data.txn_signature;
            }
            return message;
        }
        toObject() {
            const data: {
                slot?: number;
                lamports?: number;
                rent_epoch?: number;
                write_version?: number;
                executable?: boolean;
                pubkey?: Uint8Array;
                owner?: Uint8Array;
                data?: Uint8Array;
                txn_signature?: Uint8Array;
            } = {};
            if (this.slot != null) {
                data.slot = this.slot;
            }
            if (this.lamports != null) {
                data.lamports = this.lamports;
            }
            if (this.rent_epoch != null) {
                data.rent_epoch = this.rent_epoch;
            }
            if (this.write_version != null) {
                data.write_version = this.write_version;
            }
            if (this.executable != null) {
                data.executable = this.executable;
            }
            if (this.pubkey != null) {
                data.pubkey = this.pubkey;
            }
            if (this.owner != null) {
                data.owner = this.owner;
            }
            if (this.data != null) {
                data.data = this.data;
            }
            if (this.txn_signature != null) {
                data.txn_signature = this.txn_signature;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.slot != 0)
                writer.writeUint64(1, this.slot);
            if (this.lamports != 0)
                writer.writeUint64(2, this.lamports);
            if (this.rent_epoch != 0)
                writer.writeUint64(3, this.rent_epoch);
            if (this.write_version != 0)
                writer.writeUint64(4, this.write_version);
            if (this.executable != false)
                writer.writeBool(5, this.executable);
            if (this.pubkey.length)
                writer.writeBytes(6, this.pubkey);
            if (this.owner.length)
                writer.writeBytes(7, this.owner);
            if (this.data.length)
                writer.writeBytes(8, this.data);
            if (this.has_txn_signature)
                writer.writeBytes(9, this.txn_signature);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): UpdateAccountEvent {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new UpdateAccountEvent();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.slot = reader.readUint64();
                        break;
                    case 2:
                        message.lamports = reader.readUint64();
                        break;
                    case 3:
                        message.rent_epoch = reader.readUint64();
                        break;
                    case 4:
                        message.write_version = reader.readUint64();
                        break;
                    case 5:
                        message.executable = reader.readBool();
                        break;
                    case 6:
                        message.pubkey = reader.readBytes();
                        break;
                    case 7:
                        message.owner = reader.readBytes();
                        break;
                    case 8:
                        message.data = reader.readBytes();
                        break;
                    case 9:
                        message.txn_signature = reader.readBytes();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): UpdateAccountEvent {
            return UpdateAccountEvent.deserialize(bytes);
        }
    }
    export class SlotStatusEvent extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            slot?: number;
            parent?: number;
            status?: SlotStatus;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("slot" in data && data.slot != undefined) {
                    this.slot = data.slot;
                }
                if ("parent" in data && data.parent != undefined) {
                    this.parent = data.parent;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
            }
        }
        get slot() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set slot(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get parent() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set parent(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 3, SlotStatus.PROCESSED) as SlotStatus;
        }
        set status(value: SlotStatus) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            slot?: number;
            parent?: number;
            status?: SlotStatus;
        }): SlotStatusEvent {
            const message = new SlotStatusEvent({});
            if (data.slot != null) {
                message.slot = data.slot;
            }
            if (data.parent != null) {
                message.parent = data.parent;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            return message;
        }
        toObject() {
            const data: {
                slot?: number;
                parent?: number;
                status?: SlotStatus;
            } = {};
            if (this.slot != null) {
                data.slot = this.slot;
            }
            if (this.parent != null) {
                data.parent = this.parent;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.slot != 0)
                writer.writeUint64(1, this.slot);
            if (this.parent != 0)
                writer.writeUint64(2, this.parent);
            if (this.status != SlotStatus.PROCESSED)
                writer.writeEnum(3, this.status);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SlotStatusEvent {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SlotStatusEvent();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.slot = reader.readUint64();
                        break;
                    case 2:
                        message.parent = reader.readUint64();
                        break;
                    case 3:
                        message.status = reader.readEnum();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SlotStatusEvent {
            return SlotStatusEvent.deserialize(bytes);
        }
    }
    export class MessageHeader extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            num_required_signatures?: number;
            num_readonly_signed_accounts?: number;
            num_readonly_unsigned_accounts?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("num_required_signatures" in data && data.num_required_signatures != undefined) {
                    this.num_required_signatures = data.num_required_signatures;
                }
                if ("num_readonly_signed_accounts" in data && data.num_readonly_signed_accounts != undefined) {
                    this.num_readonly_signed_accounts = data.num_readonly_signed_accounts;
                }
                if ("num_readonly_unsigned_accounts" in data && data.num_readonly_unsigned_accounts != undefined) {
                    this.num_readonly_unsigned_accounts = data.num_readonly_unsigned_accounts;
                }
            }
        }
        get num_required_signatures() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set num_required_signatures(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get num_readonly_signed_accounts() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set num_readonly_signed_accounts(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get num_readonly_unsigned_accounts() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set num_readonly_unsigned_accounts(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            num_required_signatures?: number;
            num_readonly_signed_accounts?: number;
            num_readonly_unsigned_accounts?: number;
        }): MessageHeader {
            const message = new MessageHeader({});
            if (data.num_required_signatures != null) {
                message.num_required_signatures = data.num_required_signatures;
            }
            if (data.num_readonly_signed_accounts != null) {
                message.num_readonly_signed_accounts = data.num_readonly_signed_accounts;
            }
            if (data.num_readonly_unsigned_accounts != null) {
                message.num_readonly_unsigned_accounts = data.num_readonly_unsigned_accounts;
            }
            return message;
        }
        toObject() {
            const data: {
                num_required_signatures?: number;
                num_readonly_signed_accounts?: number;
                num_readonly_unsigned_accounts?: number;
            } = {};
            if (this.num_required_signatures != null) {
                data.num_required_signatures = this.num_required_signatures;
            }
            if (this.num_readonly_signed_accounts != null) {
                data.num_readonly_signed_accounts = this.num_readonly_signed_accounts;
            }
            if (this.num_readonly_unsigned_accounts != null) {
                data.num_readonly_unsigned_accounts = this.num_readonly_unsigned_accounts;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.num_required_signatures != 0)
                writer.writeUint32(1, this.num_required_signatures);
            if (this.num_readonly_signed_accounts != 0)
                writer.writeUint32(2, this.num_readonly_signed_accounts);
            if (this.num_readonly_unsigned_accounts != 0)
                writer.writeUint32(3, this.num_readonly_unsigned_accounts);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MessageHeader {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new MessageHeader();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.num_required_signatures = reader.readUint32();
                        break;
                    case 2:
                        message.num_readonly_signed_accounts = reader.readUint32();
                        break;
                    case 3:
                        message.num_readonly_unsigned_accounts = reader.readUint32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): MessageHeader {
            return MessageHeader.deserialize(bytes);
        }
    }
    export class CompiledInstruction extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            program_id_index?: number;
            data?: Uint8Array;
            accounts?: number[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [3], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("program_id_index" in data && data.program_id_index != undefined) {
                    this.program_id_index = data.program_id_index;
                }
                if ("data" in data && data.data != undefined) {
                    this.data = data.data;
                }
                if ("accounts" in data && data.accounts != undefined) {
                    this.accounts = data.accounts;
                }
            }
        }
        get program_id_index() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set program_id_index(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get data() {
            return pb_1.Message.getFieldWithDefault(this, 2, new Uint8Array(0)) as Uint8Array;
        }
        set data(value: Uint8Array) {
            pb_1.Message.setField(this, 2, value);
        }
        get accounts() {
            return pb_1.Message.getFieldWithDefault(this, 3, []) as number[];
        }
        set accounts(value: number[]) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            program_id_index?: number;
            data?: Uint8Array;
            accounts?: number[];
        }): CompiledInstruction {
            const message = new CompiledInstruction({});
            if (data.program_id_index != null) {
                message.program_id_index = data.program_id_index;
            }
            if (data.data != null) {
                message.data = data.data;
            }
            if (data.accounts != null) {
                message.accounts = data.accounts;
            }
            return message;
        }
        toObject() {
            const data: {
                program_id_index?: number;
                data?: Uint8Array;
                accounts?: number[];
            } = {};
            if (this.program_id_index != null) {
                data.program_id_index = this.program_id_index;
            }
            if (this.data != null) {
                data.data = this.data;
            }
            if (this.accounts != null) {
                data.accounts = this.accounts;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.program_id_index != 0)
                writer.writeUint32(1, this.program_id_index);
            if (this.data.length)
                writer.writeBytes(2, this.data);
            if (this.accounts.length)
                writer.writePackedUint32(3, this.accounts);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): CompiledInstruction {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new CompiledInstruction();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.program_id_index = reader.readUint32();
                        break;
                    case 2:
                        message.data = reader.readBytes();
                        break;
                    case 3:
                        message.accounts = reader.readPackedUint32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): CompiledInstruction {
            return CompiledInstruction.deserialize(bytes);
        }
    }
    export class LoadedAddresses extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            writable?: Uint8Array[];
            readonly?: Uint8Array[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1, 2], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("writable" in data && data.writable != undefined) {
                    this.writable = data.writable;
                }
                if ("readonly" in data && data.readonly != undefined) {
                    this.readonly = data.readonly;
                }
            }
        }
        get writable() {
            return pb_1.Message.getFieldWithDefault(this, 1, []) as Uint8Array[];
        }
        set writable(value: Uint8Array[]) {
            pb_1.Message.setField(this, 1, value);
        }
        get readonly() {
            return pb_1.Message.getFieldWithDefault(this, 2, []) as Uint8Array[];
        }
        set readonly(value: Uint8Array[]) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            writable?: Uint8Array[];
            readonly?: Uint8Array[];
        }): LoadedAddresses {
            const message = new LoadedAddresses({});
            if (data.writable != null) {
                message.writable = data.writable;
            }
            if (data.readonly != null) {
                message.readonly = data.readonly;
            }
            return message;
        }
        toObject() {
            const data: {
                writable?: Uint8Array[];
                readonly?: Uint8Array[];
            } = {};
            if (this.writable != null) {
                data.writable = this.writable;
            }
            if (this.readonly != null) {
                data.readonly = this.readonly;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.writable.length)
                writer.writeRepeatedBytes(1, this.writable);
            if (this.readonly.length)
                writer.writeRepeatedBytes(2, this.readonly);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): LoadedAddresses {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new LoadedAddresses();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        pb_1.Message.addToRepeatedField(message, 1, reader.readBytes());
                        break;
                    case 2:
                        pb_1.Message.addToRepeatedField(message, 2, reader.readBytes());
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): LoadedAddresses {
            return LoadedAddresses.deserialize(bytes);
        }
    }
    export class MessageAddressTableLookup extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            account_key?: Uint8Array;
            writable_indexes?: Uint8Array;
            readonly_indexes?: Uint8Array;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("account_key" in data && data.account_key != undefined) {
                    this.account_key = data.account_key;
                }
                if ("writable_indexes" in data && data.writable_indexes != undefined) {
                    this.writable_indexes = data.writable_indexes;
                }
                if ("readonly_indexes" in data && data.readonly_indexes != undefined) {
                    this.readonly_indexes = data.readonly_indexes;
                }
            }
        }
        get account_key() {
            return pb_1.Message.getFieldWithDefault(this, 1, new Uint8Array(0)) as Uint8Array;
        }
        set account_key(value: Uint8Array) {
            pb_1.Message.setField(this, 1, value);
        }
        get writable_indexes() {
            return pb_1.Message.getFieldWithDefault(this, 2, new Uint8Array(0)) as Uint8Array;
        }
        set writable_indexes(value: Uint8Array) {
            pb_1.Message.setField(this, 2, value);
        }
        get readonly_indexes() {
            return pb_1.Message.getFieldWithDefault(this, 3, new Uint8Array(0)) as Uint8Array;
        }
        set readonly_indexes(value: Uint8Array) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            account_key?: Uint8Array;
            writable_indexes?: Uint8Array;
            readonly_indexes?: Uint8Array;
        }): MessageAddressTableLookup {
            const message = new MessageAddressTableLookup({});
            if (data.account_key != null) {
                message.account_key = data.account_key;
            }
            if (data.writable_indexes != null) {
                message.writable_indexes = data.writable_indexes;
            }
            if (data.readonly_indexes != null) {
                message.readonly_indexes = data.readonly_indexes;
            }
            return message;
        }
        toObject() {
            const data: {
                account_key?: Uint8Array;
                writable_indexes?: Uint8Array;
                readonly_indexes?: Uint8Array;
            } = {};
            if (this.account_key != null) {
                data.account_key = this.account_key;
            }
            if (this.writable_indexes != null) {
                data.writable_indexes = this.writable_indexes;
            }
            if (this.readonly_indexes != null) {
                data.readonly_indexes = this.readonly_indexes;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.account_key.length)
                writer.writeBytes(1, this.account_key);
            if (this.writable_indexes.length)
                writer.writeBytes(2, this.writable_indexes);
            if (this.readonly_indexes.length)
                writer.writeBytes(3, this.readonly_indexes);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MessageAddressTableLookup {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new MessageAddressTableLookup();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.account_key = reader.readBytes();
                        break;
                    case 2:
                        message.writable_indexes = reader.readBytes();
                        break;
                    case 3:
                        message.readonly_indexes = reader.readBytes();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): MessageAddressTableLookup {
            return MessageAddressTableLookup.deserialize(bytes);
        }
    }
    export class Message extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            version?: number;
            header?: MessageHeader;
            recent_block_hash?: Uint8Array;
            account_keys?: Uint8Array[];
            instructions?: CompiledInstruction[];
            address_table_lookups?: MessageAddressTableLookup[];
            loaded_addresses?: LoadedAddresses;
            is_writable?: boolean[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [4, 5, 6, 8], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("version" in data && data.version != undefined) {
                    this.version = data.version;
                }
                if ("header" in data && data.header != undefined) {
                    this.header = data.header;
                }
                if ("recent_block_hash" in data && data.recent_block_hash != undefined) {
                    this.recent_block_hash = data.recent_block_hash;
                }
                if ("account_keys" in data && data.account_keys != undefined) {
                    this.account_keys = data.account_keys;
                }
                if ("instructions" in data && data.instructions != undefined) {
                    this.instructions = data.instructions;
                }
                if ("address_table_lookups" in data && data.address_table_lookups != undefined) {
                    this.address_table_lookups = data.address_table_lookups;
                }
                if ("loaded_addresses" in data && data.loaded_addresses != undefined) {
                    this.loaded_addresses = data.loaded_addresses;
                }
                if ("is_writable" in data && data.is_writable != undefined) {
                    this.is_writable = data.is_writable;
                }
            }
        }
        get version() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set version(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get header() {
            return pb_1.Message.getWrapperField(this, MessageHeader, 2) as MessageHeader;
        }
        set header(value: MessageHeader) {
            pb_1.Message.setWrapperField(this, 2, value);
        }
        get has_header() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get recent_block_hash() {
            return pb_1.Message.getFieldWithDefault(this, 3, new Uint8Array(0)) as Uint8Array;
        }
        set recent_block_hash(value: Uint8Array) {
            pb_1.Message.setField(this, 3, value);
        }
        get account_keys() {
            return pb_1.Message.getFieldWithDefault(this, 4, []) as Uint8Array[];
        }
        set account_keys(value: Uint8Array[]) {
            pb_1.Message.setField(this, 4, value);
        }
        get instructions() {
            return pb_1.Message.getRepeatedWrapperField(this, CompiledInstruction, 5) as CompiledInstruction[];
        }
        set instructions(value: CompiledInstruction[]) {
            pb_1.Message.setRepeatedWrapperField(this, 5, value);
        }
        get address_table_lookups() {
            return pb_1.Message.getRepeatedWrapperField(this, MessageAddressTableLookup, 6) as MessageAddressTableLookup[];
        }
        set address_table_lookups(value: MessageAddressTableLookup[]) {
            pb_1.Message.setRepeatedWrapperField(this, 6, value);
        }
        get loaded_addresses() {
            return pb_1.Message.getWrapperField(this, LoadedAddresses, 7) as LoadedAddresses;
        }
        set loaded_addresses(value: LoadedAddresses) {
            pb_1.Message.setWrapperField(this, 7, value);
        }
        get has_loaded_addresses() {
            return pb_1.Message.getField(this, 7) != null;
        }
        get is_writable() {
            return pb_1.Message.getFieldWithDefault(this, 8, []) as boolean[];
        }
        set is_writable(value: boolean[]) {
            pb_1.Message.setField(this, 8, value);
        }
        static fromObject(data: {
            version?: number;
            header?: ReturnType<typeof MessageHeader.prototype.toObject>;
            recent_block_hash?: Uint8Array;
            account_keys?: Uint8Array[];
            instructions?: ReturnType<typeof CompiledInstruction.prototype.toObject>[];
            address_table_lookups?: ReturnType<typeof MessageAddressTableLookup.prototype.toObject>[];
            loaded_addresses?: ReturnType<typeof LoadedAddresses.prototype.toObject>;
            is_writable?: boolean[];
        }): Message {
            const message = new Message({});
            if (data.version != null) {
                message.version = data.version;
            }
            if (data.header != null) {
                message.header = MessageHeader.fromObject(data.header);
            }
            if (data.recent_block_hash != null) {
                message.recent_block_hash = data.recent_block_hash;
            }
            if (data.account_keys != null) {
                message.account_keys = data.account_keys;
            }
            if (data.instructions != null) {
                message.instructions = data.instructions.map(item => CompiledInstruction.fromObject(item));
            }
            if (data.address_table_lookups != null) {
                message.address_table_lookups = data.address_table_lookups.map(item => MessageAddressTableLookup.fromObject(item));
            }
            if (data.loaded_addresses != null) {
                message.loaded_addresses = LoadedAddresses.fromObject(data.loaded_addresses);
            }
            if (data.is_writable != null) {
                message.is_writable = data.is_writable;
            }
            return message;
        }
        toObject() {
            const data: {
                version?: number;
                header?: ReturnType<typeof MessageHeader.prototype.toObject>;
                recent_block_hash?: Uint8Array;
                account_keys?: Uint8Array[];
                instructions?: ReturnType<typeof CompiledInstruction.prototype.toObject>[];
                address_table_lookups?: ReturnType<typeof MessageAddressTableLookup.prototype.toObject>[];
                loaded_addresses?: ReturnType<typeof LoadedAddresses.prototype.toObject>;
                is_writable?: boolean[];
            } = {};
            if (this.version != null) {
                data.version = this.version;
            }
            if (this.header != null) {
                data.header = this.header.toObject();
            }
            if (this.recent_block_hash != null) {
                data.recent_block_hash = this.recent_block_hash;
            }
            if (this.account_keys != null) {
                data.account_keys = this.account_keys;
            }
            if (this.instructions != null) {
                data.instructions = this.instructions.map((item: CompiledInstruction) => item.toObject());
            }
            if (this.address_table_lookups != null) {
                data.address_table_lookups = this.address_table_lookups.map((item: MessageAddressTableLookup) => item.toObject());
            }
            if (this.loaded_addresses != null) {
                data.loaded_addresses = this.loaded_addresses.toObject();
            }
            if (this.is_writable != null) {
                data.is_writable = this.is_writable;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.version != 0)
                writer.writeUint32(1, this.version);
            if (this.has_header)
                writer.writeMessage(2, this.header, () => this.header.serialize(writer));
            if (this.recent_block_hash.length)
                writer.writeBytes(3, this.recent_block_hash);
            if (this.account_keys.length)
                writer.writeRepeatedBytes(4, this.account_keys);
            if (this.instructions.length)
                writer.writeRepeatedMessage(5, this.instructions, (item: CompiledInstruction) => item.serialize(writer));
            if (this.address_table_lookups.length)
                writer.writeRepeatedMessage(6, this.address_table_lookups, (item: MessageAddressTableLookup) => item.serialize(writer));
            if (this.has_loaded_addresses)
                writer.writeMessage(7, this.loaded_addresses, () => this.loaded_addresses.serialize(writer));
            if (this.is_writable.length)
                writer.writePackedBool(8, this.is_writable);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Message {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Message();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.version = reader.readUint32();
                        break;
                    case 2:
                        reader.readMessage(message.header, () => message.header = MessageHeader.deserialize(reader));
                        break;
                    case 3:
                        message.recent_block_hash = reader.readBytes();
                        break;
                    case 4:
                        pb_1.Message.addToRepeatedField(message, 4, reader.readBytes());
                        break;
                    case 5:
                        reader.readMessage(message.instructions, () => pb_1.Message.addToRepeatedWrapperField(message, 5, CompiledInstruction.deserialize(reader), CompiledInstruction));
                        break;
                    case 6:
                        reader.readMessage(message.address_table_lookups, () => pb_1.Message.addToRepeatedWrapperField(message, 6, MessageAddressTableLookup.deserialize(reader), MessageAddressTableLookup));
                        break;
                    case 7:
                        reader.readMessage(message.loaded_addresses, () => message.loaded_addresses = LoadedAddresses.deserialize(reader));
                        break;
                    case 8:
                        message.is_writable = reader.readPackedBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Message {
            return Message.deserialize(bytes);
        }
    }
    export class SanitizedTransaction extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            message?: Message;
            message_hash?: Uint8Array;
            signatures?: Uint8Array[];
            is_simple_vote_transaction?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [3], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("message" in data && data.message != undefined) {
                    this.message = data.message;
                }
                if ("message_hash" in data && data.message_hash != undefined) {
                    this.message_hash = data.message_hash;
                }
                if ("signatures" in data && data.signatures != undefined) {
                    this.signatures = data.signatures;
                }
                if ("is_simple_vote_transaction" in data && data.is_simple_vote_transaction != undefined) {
                    this.is_simple_vote_transaction = data.is_simple_vote_transaction;
                }
            }
        }
        get message() {
            return pb_1.Message.getWrapperField(this, Message, 1) as Message;
        }
        set message(value: Message) {
            pb_1.Message.setWrapperField(this, 1, value);
        }
        get has_message() {
            return pb_1.Message.getField(this, 1) != null;
        }
        get message_hash() {
            return pb_1.Message.getFieldWithDefault(this, 2, new Uint8Array(0)) as Uint8Array;
        }
        set message_hash(value: Uint8Array) {
            pb_1.Message.setField(this, 2, value);
        }
        get signatures() {
            return pb_1.Message.getFieldWithDefault(this, 3, []) as Uint8Array[];
        }
        set signatures(value: Uint8Array[]) {
            pb_1.Message.setField(this, 3, value);
        }
        get is_simple_vote_transaction() {
            return pb_1.Message.getFieldWithDefault(this, 4, false) as boolean;
        }
        set is_simple_vote_transaction(value: boolean) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            message?: ReturnType<typeof Message.prototype.toObject>;
            message_hash?: Uint8Array;
            signatures?: Uint8Array[];
            is_simple_vote_transaction?: boolean;
        }): SanitizedTransaction {
            const message = new SanitizedTransaction({});
            if (data.message != null) {
                message.message = Message.fromObject(data.message);
            }
            if (data.message_hash != null) {
                message.message_hash = data.message_hash;
            }
            if (data.signatures != null) {
                message.signatures = data.signatures;
            }
            if (data.is_simple_vote_transaction != null) {
                message.is_simple_vote_transaction = data.is_simple_vote_transaction;
            }
            return message;
        }
        toObject() {
            const data: {
                message?: ReturnType<typeof Message.prototype.toObject>;
                message_hash?: Uint8Array;
                signatures?: Uint8Array[];
                is_simple_vote_transaction?: boolean;
            } = {};
            if (this.message != null) {
                data.message = this.message.toObject();
            }
            if (this.message_hash != null) {
                data.message_hash = this.message_hash;
            }
            if (this.signatures != null) {
                data.signatures = this.signatures;
            }
            if (this.is_simple_vote_transaction != null) {
                data.is_simple_vote_transaction = this.is_simple_vote_transaction;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.has_message)
                writer.writeMessage(1, this.message, () => this.message.serialize(writer));
            if (this.message_hash.length)
                writer.writeBytes(2, this.message_hash);
            if (this.signatures.length)
                writer.writeRepeatedBytes(3, this.signatures);
            if (this.is_simple_vote_transaction != false)
                writer.writeBool(4, this.is_simple_vote_transaction);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SanitizedTransaction {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SanitizedTransaction();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.message, () => message.message = Message.deserialize(reader));
                        break;
                    case 2:
                        message.message_hash = reader.readBytes();
                        break;
                    case 3:
                        pb_1.Message.addToRepeatedField(message, 3, reader.readBytes());
                        break;
                    case 4:
                        message.is_simple_vote_transaction = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SanitizedTransaction {
            return SanitizedTransaction.deserialize(bytes);
        }
    }
    export class TransactionEvent extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            slot?: number;
            signature?: Uint8Array;
            index?: number;
            is_vote?: boolean;
            transaction?: SanitizedTransaction;
            transaction_status_meta?: TransactionStatusMeta;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("slot" in data && data.slot != undefined) {
                    this.slot = data.slot;
                }
                if ("signature" in data && data.signature != undefined) {
                    this.signature = data.signature;
                }
                if ("index" in data && data.index != undefined) {
                    this.index = data.index;
                }
                if ("is_vote" in data && data.is_vote != undefined) {
                    this.is_vote = data.is_vote;
                }
                if ("transaction" in data && data.transaction != undefined) {
                    this.transaction = data.transaction;
                }
                if ("transaction_status_meta" in data && data.transaction_status_meta != undefined) {
                    this.transaction_status_meta = data.transaction_status_meta;
                }
            }
        }
        get slot() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set slot(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get signature() {
            return pb_1.Message.getFieldWithDefault(this, 2, new Uint8Array(0)) as Uint8Array;
        }
        set signature(value: Uint8Array) {
            pb_1.Message.setField(this, 2, value);
        }
        get index() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set index(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get is_vote() {
            return pb_1.Message.getFieldWithDefault(this, 4, false) as boolean;
        }
        set is_vote(value: boolean) {
            pb_1.Message.setField(this, 4, value);
        }
        get transaction() {
            return pb_1.Message.getWrapperField(this, SanitizedTransaction, 5) as SanitizedTransaction;
        }
        set transaction(value: SanitizedTransaction) {
            pb_1.Message.setWrapperField(this, 5, value);
        }
        get has_transaction() {
            return pb_1.Message.getField(this, 5) != null;
        }
        get transaction_status_meta() {
            return pb_1.Message.getWrapperField(this, TransactionStatusMeta, 6) as TransactionStatusMeta;
        }
        set transaction_status_meta(value: TransactionStatusMeta) {
            pb_1.Message.setWrapperField(this, 6, value);
        }
        get has_transaction_status_meta() {
            return pb_1.Message.getField(this, 6) != null;
        }
        static fromObject(data: {
            slot?: number;
            signature?: Uint8Array;
            index?: number;
            is_vote?: boolean;
            transaction?: ReturnType<typeof SanitizedTransaction.prototype.toObject>;
            transaction_status_meta?: ReturnType<typeof TransactionStatusMeta.prototype.toObject>;
        }): TransactionEvent {
            const message = new TransactionEvent({});
            if (data.slot != null) {
                message.slot = data.slot;
            }
            if (data.signature != null) {
                message.signature = data.signature;
            }
            if (data.index != null) {
                message.index = data.index;
            }
            if (data.is_vote != null) {
                message.is_vote = data.is_vote;
            }
            if (data.transaction != null) {
                message.transaction = SanitizedTransaction.fromObject(data.transaction);
            }
            if (data.transaction_status_meta != null) {
                message.transaction_status_meta = TransactionStatusMeta.fromObject(data.transaction_status_meta);
            }
            return message;
        }
        toObject() {
            const data: {
                slot?: number;
                signature?: Uint8Array;
                index?: number;
                is_vote?: boolean;
                transaction?: ReturnType<typeof SanitizedTransaction.prototype.toObject>;
                transaction_status_meta?: ReturnType<typeof TransactionStatusMeta.prototype.toObject>;
            } = {};
            if (this.slot != null) {
                data.slot = this.slot;
            }
            if (this.signature != null) {
                data.signature = this.signature;
            }
            if (this.index != null) {
                data.index = this.index;
            }
            if (this.is_vote != null) {
                data.is_vote = this.is_vote;
            }
            if (this.transaction != null) {
                data.transaction = this.transaction.toObject();
            }
            if (this.transaction_status_meta != null) {
                data.transaction_status_meta = this.transaction_status_meta.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.slot != 0)
                writer.writeUint64(1, this.slot);
            if (this.signature.length)
                writer.writeBytes(2, this.signature);
            if (this.index != 0)
                writer.writeUint64(3, this.index);
            if (this.is_vote != false)
                writer.writeBool(4, this.is_vote);
            if (this.has_transaction)
                writer.writeMessage(5, this.transaction, () => this.transaction.serialize(writer));
            if (this.has_transaction_status_meta)
                writer.writeMessage(6, this.transaction_status_meta, () => this.transaction_status_meta.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): TransactionEvent {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new TransactionEvent();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.slot = reader.readUint64();
                        break;
                    case 2:
                        message.signature = reader.readBytes();
                        break;
                    case 3:
                        message.index = reader.readUint64();
                        break;
                    case 4:
                        message.is_vote = reader.readBool();
                        break;
                    case 5:
                        reader.readMessage(message.transaction, () => message.transaction = SanitizedTransaction.deserialize(reader));
                        break;
                    case 6:
                        reader.readMessage(message.transaction_status_meta, () => message.transaction_status_meta = TransactionStatusMeta.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): TransactionEvent {
            return TransactionEvent.deserialize(bytes);
        }
    }
    export class InnerInstruction extends pb_1.Message {
        #one_of_decls: number[][] = [[2]];
        constructor(data?: any[] | ({
            instruction?: CompiledInstruction;
        } & (({
            stack_height?: number;
        })))) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("instruction" in data && data.instruction != undefined) {
                    this.instruction = data.instruction;
                }
                if ("stack_height" in data && data.stack_height != undefined) {
                    this.stack_height = data.stack_height;
                }
            }
        }
        get instruction() {
            return pb_1.Message.getWrapperField(this, CompiledInstruction, 1) as CompiledInstruction;
        }
        set instruction(value: CompiledInstruction) {
            pb_1.Message.setWrapperField(this, 1, value);
        }
        get has_instruction() {
            return pb_1.Message.getField(this, 1) != null;
        }
        get stack_height() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set stack_height(value: number) {
            pb_1.Message.setOneofField(this, 2, this.#one_of_decls[0], value);
        }
        get has_stack_height() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get _stack_height() {
            const cases: {
                [index: number]: "none" | "stack_height";
            } = {
                0: "none",
                2: "stack_height"
            };
            return cases[pb_1.Message.computeOneofCase(this, [2])];
        }
        static fromObject(data: {
            instruction?: ReturnType<typeof CompiledInstruction.prototype.toObject>;
            stack_height?: number;
        }): InnerInstruction {
            const message = new InnerInstruction({});
            if (data.instruction != null) {
                message.instruction = CompiledInstruction.fromObject(data.instruction);
            }
            if (data.stack_height != null) {
                message.stack_height = data.stack_height;
            }
            return message;
        }
        toObject() {
            const data: {
                instruction?: ReturnType<typeof CompiledInstruction.prototype.toObject>;
                stack_height?: number;
            } = {};
            if (this.instruction != null) {
                data.instruction = this.instruction.toObject();
            }
            if (this.stack_height != null) {
                data.stack_height = this.stack_height;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.has_instruction)
                writer.writeMessage(1, this.instruction, () => this.instruction.serialize(writer));
            if (this.has_stack_height)
                writer.writeUint32(2, this.stack_height);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): InnerInstruction {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new InnerInstruction();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.instruction, () => message.instruction = CompiledInstruction.deserialize(reader));
                        break;
                    case 2:
                        message.stack_height = reader.readUint32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): InnerInstruction {
            return InnerInstruction.deserialize(bytes);
        }
    }
    export class InnerInstructions extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            index?: number;
            instructions?: InnerInstruction[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [2], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("index" in data && data.index != undefined) {
                    this.index = data.index;
                }
                if ("instructions" in data && data.instructions != undefined) {
                    this.instructions = data.instructions;
                }
            }
        }
        get index() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set index(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get instructions() {
            return pb_1.Message.getRepeatedWrapperField(this, InnerInstruction, 2) as InnerInstruction[];
        }
        set instructions(value: InnerInstruction[]) {
            pb_1.Message.setRepeatedWrapperField(this, 2, value);
        }
        static fromObject(data: {
            index?: number;
            instructions?: ReturnType<typeof InnerInstruction.prototype.toObject>[];
        }): InnerInstructions {
            const message = new InnerInstructions({});
            if (data.index != null) {
                message.index = data.index;
            }
            if (data.instructions != null) {
                message.instructions = data.instructions.map(item => InnerInstruction.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                index?: number;
                instructions?: ReturnType<typeof InnerInstruction.prototype.toObject>[];
            } = {};
            if (this.index != null) {
                data.index = this.index;
            }
            if (this.instructions != null) {
                data.instructions = this.instructions.map((item: InnerInstruction) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.index != 0)
                writer.writeUint32(1, this.index);
            if (this.instructions.length)
                writer.writeRepeatedMessage(2, this.instructions, (item: InnerInstruction) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): InnerInstructions {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new InnerInstructions();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.index = reader.readUint32();
                        break;
                    case 2:
                        reader.readMessage(message.instructions, () => pb_1.Message.addToRepeatedWrapperField(message, 2, InnerInstruction.deserialize(reader), InnerInstruction));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): InnerInstructions {
            return InnerInstructions.deserialize(bytes);
        }
    }
    export class UiTokenAmount extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            ui_amount?: number;
            decimals?: number;
            amount?: string;
            ui_amount_string?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("ui_amount" in data && data.ui_amount != undefined) {
                    this.ui_amount = data.ui_amount;
                }
                if ("decimals" in data && data.decimals != undefined) {
                    this.decimals = data.decimals;
                }
                if ("amount" in data && data.amount != undefined) {
                    this.amount = data.amount;
                }
                if ("ui_amount_string" in data && data.ui_amount_string != undefined) {
                    this.ui_amount_string = data.ui_amount_string;
                }
            }
        }
        get ui_amount() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set ui_amount(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get decimals() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set decimals(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get amount() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set amount(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get ui_amount_string() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set ui_amount_string(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            ui_amount?: number;
            decimals?: number;
            amount?: string;
            ui_amount_string?: string;
        }): UiTokenAmount {
            const message = new UiTokenAmount({});
            if (data.ui_amount != null) {
                message.ui_amount = data.ui_amount;
            }
            if (data.decimals != null) {
                message.decimals = data.decimals;
            }
            if (data.amount != null) {
                message.amount = data.amount;
            }
            if (data.ui_amount_string != null) {
                message.ui_amount_string = data.ui_amount_string;
            }
            return message;
        }
        toObject() {
            const data: {
                ui_amount?: number;
                decimals?: number;
                amount?: string;
                ui_amount_string?: string;
            } = {};
            if (this.ui_amount != null) {
                data.ui_amount = this.ui_amount;
            }
            if (this.decimals != null) {
                data.decimals = this.decimals;
            }
            if (this.amount != null) {
                data.amount = this.amount;
            }
            if (this.ui_amount_string != null) {
                data.ui_amount_string = this.ui_amount_string;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.ui_amount != 0)
                writer.writeDouble(1, this.ui_amount);
            if (this.decimals != 0)
                writer.writeUint32(2, this.decimals);
            if (this.amount.length)
                writer.writeString(3, this.amount);
            if (this.ui_amount_string.length)
                writer.writeString(4, this.ui_amount_string);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): UiTokenAmount {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new UiTokenAmount();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.ui_amount = reader.readDouble();
                        break;
                    case 2:
                        message.decimals = reader.readUint32();
                        break;
                    case 3:
                        message.amount = reader.readString();
                        break;
                    case 4:
                        message.ui_amount_string = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): UiTokenAmount {
            return UiTokenAmount.deserialize(bytes);
        }
    }
    export class TransactionTokenBalance extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            account_index?: number;
            mint?: string;
            ui_token_amount?: UiTokenAmount;
            owner?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("account_index" in data && data.account_index != undefined) {
                    this.account_index = data.account_index;
                }
                if ("mint" in data && data.mint != undefined) {
                    this.mint = data.mint;
                }
                if ("ui_token_amount" in data && data.ui_token_amount != undefined) {
                    this.ui_token_amount = data.ui_token_amount;
                }
                if ("owner" in data && data.owner != undefined) {
                    this.owner = data.owner;
                }
            }
        }
        get account_index() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set account_index(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get mint() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set mint(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get ui_token_amount() {
            return pb_1.Message.getWrapperField(this, UiTokenAmount, 3) as UiTokenAmount;
        }
        set ui_token_amount(value: UiTokenAmount) {
            pb_1.Message.setWrapperField(this, 3, value);
        }
        get has_ui_token_amount() {
            return pb_1.Message.getField(this, 3) != null;
        }
        get owner() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set owner(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            account_index?: number;
            mint?: string;
            ui_token_amount?: ReturnType<typeof UiTokenAmount.prototype.toObject>;
            owner?: string;
        }): TransactionTokenBalance {
            const message = new TransactionTokenBalance({});
            if (data.account_index != null) {
                message.account_index = data.account_index;
            }
            if (data.mint != null) {
                message.mint = data.mint;
            }
            if (data.ui_token_amount != null) {
                message.ui_token_amount = UiTokenAmount.fromObject(data.ui_token_amount);
            }
            if (data.owner != null) {
                message.owner = data.owner;
            }
            return message;
        }
        toObject() {
            const data: {
                account_index?: number;
                mint?: string;
                ui_token_amount?: ReturnType<typeof UiTokenAmount.prototype.toObject>;
                owner?: string;
            } = {};
            if (this.account_index != null) {
                data.account_index = this.account_index;
            }
            if (this.mint != null) {
                data.mint = this.mint;
            }
            if (this.ui_token_amount != null) {
                data.ui_token_amount = this.ui_token_amount.toObject();
            }
            if (this.owner != null) {
                data.owner = this.owner;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.account_index != 0)
                writer.writeUint32(1, this.account_index);
            if (this.mint.length)
                writer.writeString(2, this.mint);
            if (this.has_ui_token_amount)
                writer.writeMessage(3, this.ui_token_amount, () => this.ui_token_amount.serialize(writer));
            if (this.owner.length)
                writer.writeString(4, this.owner);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): TransactionTokenBalance {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new TransactionTokenBalance();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.account_index = reader.readUint32();
                        break;
                    case 2:
                        message.mint = reader.readString();
                        break;
                    case 3:
                        reader.readMessage(message.ui_token_amount, () => message.ui_token_amount = UiTokenAmount.deserialize(reader));
                        break;
                    case 4:
                        message.owner = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): TransactionTokenBalance {
            return TransactionTokenBalance.deserialize(bytes);
        }
    }
    export class Reward extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            pubkey?: string;
            lamports?: number;
            post_balance?: number;
            reward_type?: number;
            commission?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("pubkey" in data && data.pubkey != undefined) {
                    this.pubkey = data.pubkey;
                }
                if ("lamports" in data && data.lamports != undefined) {
                    this.lamports = data.lamports;
                }
                if ("post_balance" in data && data.post_balance != undefined) {
                    this.post_balance = data.post_balance;
                }
                if ("reward_type" in data && data.reward_type != undefined) {
                    this.reward_type = data.reward_type;
                }
                if ("commission" in data && data.commission != undefined) {
                    this.commission = data.commission;
                }
            }
        }
        get pubkey() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set pubkey(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get lamports() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set lamports(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get post_balance() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set post_balance(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get reward_type() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set reward_type(value: number) {
            pb_1.Message.setField(this, 4, value);
        }
        get commission() {
            return pb_1.Message.getFieldWithDefault(this, 5, 0) as number;
        }
        set commission(value: number) {
            pb_1.Message.setField(this, 5, value);
        }
        static fromObject(data: {
            pubkey?: string;
            lamports?: number;
            post_balance?: number;
            reward_type?: number;
            commission?: number;
        }): Reward {
            const message = new Reward({});
            if (data.pubkey != null) {
                message.pubkey = data.pubkey;
            }
            if (data.lamports != null) {
                message.lamports = data.lamports;
            }
            if (data.post_balance != null) {
                message.post_balance = data.post_balance;
            }
            if (data.reward_type != null) {
                message.reward_type = data.reward_type;
            }
            if (data.commission != null) {
                message.commission = data.commission;
            }
            return message;
        }
        toObject() {
            const data: {
                pubkey?: string;
                lamports?: number;
                post_balance?: number;
                reward_type?: number;
                commission?: number;
            } = {};
            if (this.pubkey != null) {
                data.pubkey = this.pubkey;
            }
            if (this.lamports != null) {
                data.lamports = this.lamports;
            }
            if (this.post_balance != null) {
                data.post_balance = this.post_balance;
            }
            if (this.reward_type != null) {
                data.reward_type = this.reward_type;
            }
            if (this.commission != null) {
                data.commission = this.commission;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.pubkey.length)
                writer.writeString(1, this.pubkey);
            if (this.lamports != 0)
                writer.writeInt64(2, this.lamports);
            if (this.post_balance != 0)
                writer.writeUint64(3, this.post_balance);
            if (this.reward_type != 0)
                writer.writeInt32(4, this.reward_type);
            if (this.commission != 0)
                writer.writeUint32(5, this.commission);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Reward {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Reward();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.pubkey = reader.readString();
                        break;
                    case 2:
                        message.lamports = reader.readInt64();
                        break;
                    case 3:
                        message.post_balance = reader.readUint64();
                        break;
                    case 4:
                        message.reward_type = reader.readInt32();
                        break;
                    case 5:
                        message.commission = reader.readUint32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Reward {
            return Reward.deserialize(bytes);
        }
    }
    export class TransactionStatusMeta extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            is_status_err?: boolean;
            fee?: number;
            pre_balances?: number[];
            post_balances?: number[];
            inner_instructions?: InnerInstructions[];
            log_messages?: string[];
            pre_token_balances?: TransactionTokenBalance[];
            post_token_balances?: TransactionTokenBalance[];
            rewards?: Reward[];
            error_info?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [3, 4, 5, 6, 7, 8, 9], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("is_status_err" in data && data.is_status_err != undefined) {
                    this.is_status_err = data.is_status_err;
                }
                if ("fee" in data && data.fee != undefined) {
                    this.fee = data.fee;
                }
                if ("pre_balances" in data && data.pre_balances != undefined) {
                    this.pre_balances = data.pre_balances;
                }
                if ("post_balances" in data && data.post_balances != undefined) {
                    this.post_balances = data.post_balances;
                }
                if ("inner_instructions" in data && data.inner_instructions != undefined) {
                    this.inner_instructions = data.inner_instructions;
                }
                if ("log_messages" in data && data.log_messages != undefined) {
                    this.log_messages = data.log_messages;
                }
                if ("pre_token_balances" in data && data.pre_token_balances != undefined) {
                    this.pre_token_balances = data.pre_token_balances;
                }
                if ("post_token_balances" in data && data.post_token_balances != undefined) {
                    this.post_token_balances = data.post_token_balances;
                }
                if ("rewards" in data && data.rewards != undefined) {
                    this.rewards = data.rewards;
                }
                if ("error_info" in data && data.error_info != undefined) {
                    this.error_info = data.error_info;
                }
            }
        }
        get is_status_err() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set is_status_err(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        get fee() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set fee(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get pre_balances() {
            return pb_1.Message.getFieldWithDefault(this, 3, []) as number[];
        }
        set pre_balances(value: number[]) {
            pb_1.Message.setField(this, 3, value);
        }
        get post_balances() {
            return pb_1.Message.getFieldWithDefault(this, 4, []) as number[];
        }
        set post_balances(value: number[]) {
            pb_1.Message.setField(this, 4, value);
        }
        get inner_instructions() {
            return pb_1.Message.getRepeatedWrapperField(this, InnerInstructions, 5) as InnerInstructions[];
        }
        set inner_instructions(value: InnerInstructions[]) {
            pb_1.Message.setRepeatedWrapperField(this, 5, value);
        }
        get log_messages() {
            return pb_1.Message.getFieldWithDefault(this, 6, []) as string[];
        }
        set log_messages(value: string[]) {
            pb_1.Message.setField(this, 6, value);
        }
        get pre_token_balances() {
            return pb_1.Message.getRepeatedWrapperField(this, TransactionTokenBalance, 7) as TransactionTokenBalance[];
        }
        set pre_token_balances(value: TransactionTokenBalance[]) {
            pb_1.Message.setRepeatedWrapperField(this, 7, value);
        }
        get post_token_balances() {
            return pb_1.Message.getRepeatedWrapperField(this, TransactionTokenBalance, 8) as TransactionTokenBalance[];
        }
        set post_token_balances(value: TransactionTokenBalance[]) {
            pb_1.Message.setRepeatedWrapperField(this, 8, value);
        }
        get rewards() {
            return pb_1.Message.getRepeatedWrapperField(this, Reward, 9) as Reward[];
        }
        set rewards(value: Reward[]) {
            pb_1.Message.setRepeatedWrapperField(this, 9, value);
        }
        get error_info() {
            return pb_1.Message.getFieldWithDefault(this, 10, "") as string;
        }
        set error_info(value: string) {
            pb_1.Message.setField(this, 10, value);
        }
        static fromObject(data: {
            is_status_err?: boolean;
            fee?: number;
            pre_balances?: number[];
            post_balances?: number[];
            inner_instructions?: ReturnType<typeof InnerInstructions.prototype.toObject>[];
            log_messages?: string[];
            pre_token_balances?: ReturnType<typeof TransactionTokenBalance.prototype.toObject>[];
            post_token_balances?: ReturnType<typeof TransactionTokenBalance.prototype.toObject>[];
            rewards?: ReturnType<typeof Reward.prototype.toObject>[];
            error_info?: string;
        }): TransactionStatusMeta {
            const message = new TransactionStatusMeta({});
            if (data.is_status_err != null) {
                message.is_status_err = data.is_status_err;
            }
            if (data.fee != null) {
                message.fee = data.fee;
            }
            if (data.pre_balances != null) {
                message.pre_balances = data.pre_balances;
            }
            if (data.post_balances != null) {
                message.post_balances = data.post_balances;
            }
            if (data.inner_instructions != null) {
                message.inner_instructions = data.inner_instructions.map(item => InnerInstructions.fromObject(item));
            }
            if (data.log_messages != null) {
                message.log_messages = data.log_messages;
            }
            if (data.pre_token_balances != null) {
                message.pre_token_balances = data.pre_token_balances.map(item => TransactionTokenBalance.fromObject(item));
            }
            if (data.post_token_balances != null) {
                message.post_token_balances = data.post_token_balances.map(item => TransactionTokenBalance.fromObject(item));
            }
            if (data.rewards != null) {
                message.rewards = data.rewards.map(item => Reward.fromObject(item));
            }
            if (data.error_info != null) {
                message.error_info = data.error_info;
            }
            return message;
        }
        toObject() {
            const data: {
                is_status_err?: boolean;
                fee?: number;
                pre_balances?: number[];
                post_balances?: number[];
                inner_instructions?: ReturnType<typeof InnerInstructions.prototype.toObject>[];
                log_messages?: string[];
                pre_token_balances?: ReturnType<typeof TransactionTokenBalance.prototype.toObject>[];
                post_token_balances?: ReturnType<typeof TransactionTokenBalance.prototype.toObject>[];
                rewards?: ReturnType<typeof Reward.prototype.toObject>[];
                error_info?: string;
            } = {};
            if (this.is_status_err != null) {
                data.is_status_err = this.is_status_err;
            }
            if (this.fee != null) {
                data.fee = this.fee;
            }
            if (this.pre_balances != null) {
                data.pre_balances = this.pre_balances;
            }
            if (this.post_balances != null) {
                data.post_balances = this.post_balances;
            }
            if (this.inner_instructions != null) {
                data.inner_instructions = this.inner_instructions.map((item: InnerInstructions) => item.toObject());
            }
            if (this.log_messages != null) {
                data.log_messages = this.log_messages;
            }
            if (this.pre_token_balances != null) {
                data.pre_token_balances = this.pre_token_balances.map((item: TransactionTokenBalance) => item.toObject());
            }
            if (this.post_token_balances != null) {
                data.post_token_balances = this.post_token_balances.map((item: TransactionTokenBalance) => item.toObject());
            }
            if (this.rewards != null) {
                data.rewards = this.rewards.map((item: Reward) => item.toObject());
            }
            if (this.error_info != null) {
                data.error_info = this.error_info;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.is_status_err != false)
                writer.writeBool(1, this.is_status_err);
            if (this.fee != 0)
                writer.writeUint64(2, this.fee);
            if (this.pre_balances.length)
                writer.writePackedUint64(3, this.pre_balances);
            if (this.post_balances.length)
                writer.writePackedUint64(4, this.post_balances);
            if (this.inner_instructions.length)
                writer.writeRepeatedMessage(5, this.inner_instructions, (item: InnerInstructions) => item.serialize(writer));
            if (this.log_messages.length)
                writer.writeRepeatedString(6, this.log_messages);
            if (this.pre_token_balances.length)
                writer.writeRepeatedMessage(7, this.pre_token_balances, (item: TransactionTokenBalance) => item.serialize(writer));
            if (this.post_token_balances.length)
                writer.writeRepeatedMessage(8, this.post_token_balances, (item: TransactionTokenBalance) => item.serialize(writer));
            if (this.rewards.length)
                writer.writeRepeatedMessage(9, this.rewards, (item: Reward) => item.serialize(writer));
            if (this.error_info.length)
                writer.writeString(10, this.error_info);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): TransactionStatusMeta {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new TransactionStatusMeta();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.is_status_err = reader.readBool();
                        break;
                    case 2:
                        message.fee = reader.readUint64();
                        break;
                    case 3:
                        message.pre_balances = reader.readPackedUint64();
                        break;
                    case 4:
                        message.post_balances = reader.readPackedUint64();
                        break;
                    case 5:
                        reader.readMessage(message.inner_instructions, () => pb_1.Message.addToRepeatedWrapperField(message, 5, InnerInstructions.deserialize(reader), InnerInstructions));
                        break;
                    case 6:
                        pb_1.Message.addToRepeatedField(message, 6, reader.readString());
                        break;
                    case 7:
                        reader.readMessage(message.pre_token_balances, () => pb_1.Message.addToRepeatedWrapperField(message, 7, TransactionTokenBalance.deserialize(reader), TransactionTokenBalance));
                        break;
                    case 8:
                        reader.readMessage(message.post_token_balances, () => pb_1.Message.addToRepeatedWrapperField(message, 8, TransactionTokenBalance.deserialize(reader), TransactionTokenBalance));
                        break;
                    case 9:
                        reader.readMessage(message.rewards, () => pb_1.Message.addToRepeatedWrapperField(message, 9, Reward.deserialize(reader), Reward));
                        break;
                    case 10:
                        message.error_info = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): TransactionStatusMeta {
            return TransactionStatusMeta.deserialize(bytes);
        }
    }
    export class TransactionEventWrapper extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            stream_type?: StreamType;
            transaction?: TransactionEvent;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("stream_type" in data && data.stream_type != undefined) {
                    this.stream_type = data.stream_type;
                }
                if ("transaction" in data && data.transaction != undefined) {
                    this.transaction = data.transaction;
                }
            }
        }
        get stream_type() {
            return pb_1.Message.getFieldWithDefault(this, 1, StreamType.STREAM_TYPE_UNSPECIFIED) as StreamType;
        }
        set stream_type(value: StreamType) {
            pb_1.Message.setField(this, 1, value);
        }
        get transaction() {
            return pb_1.Message.getWrapperField(this, TransactionEvent, 2) as TransactionEvent;
        }
        set transaction(value: TransactionEvent) {
            pb_1.Message.setWrapperField(this, 2, value);
        }
        get has_transaction() {
            return pb_1.Message.getField(this, 2) != null;
        }
        static fromObject(data: {
            stream_type?: StreamType;
            transaction?: ReturnType<typeof TransactionEvent.prototype.toObject>;
        }): TransactionEventWrapper {
            const message = new TransactionEventWrapper({});
            if (data.stream_type != null) {
                message.stream_type = data.stream_type;
            }
            if (data.transaction != null) {
                message.transaction = TransactionEvent.fromObject(data.transaction);
            }
            return message;
        }
        toObject() {
            const data: {
                stream_type?: StreamType;
                transaction?: ReturnType<typeof TransactionEvent.prototype.toObject>;
            } = {};
            if (this.stream_type != null) {
                data.stream_type = this.stream_type;
            }
            if (this.transaction != null) {
                data.transaction = this.transaction.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.stream_type != StreamType.STREAM_TYPE_UNSPECIFIED)
                writer.writeEnum(1, this.stream_type);
            if (this.has_transaction)
                writer.writeMessage(2, this.transaction, () => this.transaction.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): TransactionEventWrapper {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new TransactionEventWrapper();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.stream_type = reader.readEnum();
                        break;
                    case 2:
                        reader.readMessage(message.transaction, () => message.transaction = TransactionEvent.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): TransactionEventWrapper {
            return TransactionEventWrapper.deserialize(bytes);
        }
    }
    export class MessageWrapper extends pb_1.Message {
        #one_of_decls: number[][] = [[1, 2, 3]];
        constructor(data?: any[] | ({} & (({
            account?: UpdateAccountEvent;
            slot?: never;
            transaction?: never;
        } | {
            account?: never;
            slot?: SlotStatusEvent;
            transaction?: never;
        } | {
            account?: never;
            slot?: never;
            transaction?: TransactionEventWrapper;
        })))) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("account" in data && data.account != undefined) {
                    this.account = data.account;
                }
                if ("slot" in data && data.slot != undefined) {
                    this.slot = data.slot;
                }
                if ("transaction" in data && data.transaction != undefined) {
                    this.transaction = data.transaction;
                }
            }
        }
        get account() {
            return pb_1.Message.getWrapperField(this, UpdateAccountEvent, 1) as UpdateAccountEvent;
        }
        set account(value: UpdateAccountEvent) {
            pb_1.Message.setOneofWrapperField(this, 1, this.#one_of_decls[0], value);
        }
        get has_account() {
            return pb_1.Message.getField(this, 1) != null;
        }
        get slot() {
            return pb_1.Message.getWrapperField(this, SlotStatusEvent, 2) as SlotStatusEvent;
        }
        set slot(value: SlotStatusEvent) {
            pb_1.Message.setOneofWrapperField(this, 2, this.#one_of_decls[0], value);
        }
        get has_slot() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get transaction() {
            return pb_1.Message.getWrapperField(this, TransactionEventWrapper, 3) as TransactionEventWrapper;
        }
        set transaction(value: TransactionEventWrapper) {
            pb_1.Message.setOneofWrapperField(this, 3, this.#one_of_decls[0], value);
        }
        get has_transaction() {
            return pb_1.Message.getField(this, 3) != null;
        }
        get event_message() {
            const cases: {
                [index: number]: "none" | "account" | "slot" | "transaction";
            } = {
                0: "none",
                1: "account",
                2: "slot",
                3: "transaction"
            };
            return cases[pb_1.Message.computeOneofCase(this, [1, 2, 3])];
        }
        static fromObject(data: {
            account?: ReturnType<typeof UpdateAccountEvent.prototype.toObject>;
            slot?: ReturnType<typeof SlotStatusEvent.prototype.toObject>;
            transaction?: ReturnType<typeof TransactionEventWrapper.prototype.toObject>;
        }): MessageWrapper {
            const message = new MessageWrapper({});
            if (data.account != null) {
                message.account = UpdateAccountEvent.fromObject(data.account);
            }
            if (data.slot != null) {
                message.slot = SlotStatusEvent.fromObject(data.slot);
            }
            if (data.transaction != null) {
                message.transaction = TransactionEventWrapper.fromObject(data.transaction);
            }
            return message;
        }
        toObject() {
            const data: {
                account?: ReturnType<typeof UpdateAccountEvent.prototype.toObject>;
                slot?: ReturnType<typeof SlotStatusEvent.prototype.toObject>;
                transaction?: ReturnType<typeof TransactionEventWrapper.prototype.toObject>;
            } = {};
            if (this.account != null) {
                data.account = this.account.toObject();
            }
            if (this.slot != null) {
                data.slot = this.slot.toObject();
            }
            if (this.transaction != null) {
                data.transaction = this.transaction.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.has_account)
                writer.writeMessage(1, this.account, () => this.account.serialize(writer));
            if (this.has_slot)
                writer.writeMessage(2, this.slot, () => this.slot.serialize(writer));
            if (this.has_transaction)
                writer.writeMessage(3, this.transaction, () => this.transaction.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MessageWrapper {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new MessageWrapper();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.account, () => message.account = UpdateAccountEvent.deserialize(reader));
                        break;
                    case 2:
                        reader.readMessage(message.slot, () => message.slot = SlotStatusEvent.deserialize(reader));
                        break;
                    case 3:
                        reader.readMessage(message.transaction, () => message.transaction = TransactionEventWrapper.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): MessageWrapper {
            return MessageWrapper.deserialize(bytes);
        }
    }
}
