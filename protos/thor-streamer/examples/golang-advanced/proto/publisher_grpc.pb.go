// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: publisher.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EventPublisher_SubscribeToTransactions_FullMethodName       = "/publisher.EventPublisher/SubscribeToTransactions"
	EventPublisher_SubscribeToAccountUpdates_FullMethodName     = "/publisher.EventPublisher/SubscribeToAccountUpdates"
	EventPublisher_SubscribeToSlotStatus_FullMethodName         = "/publisher.EventPublisher/SubscribeToSlotStatus"
	EventPublisher_SubscribeToWalletTransactions_FullMethodName = "/publisher.EventPublisher/SubscribeToWalletTransactions"
)

// EventPublisherClient is the client API for EventPublisher service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EventPublisherClient interface {
	SubscribeToTransactions(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error)
	SubscribeToAccountUpdates(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error)
	SubscribeToSlotStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error)
	SubscribeToWalletTransactions(ctx context.Context, in *SubscribeWalletRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error)
}

type eventPublisherClient struct {
	cc grpc.ClientConnInterface
}

func NewEventPublisherClient(cc grpc.ClientConnInterface) EventPublisherClient {
	return &eventPublisherClient{cc}
}

func (c *eventPublisherClient) SubscribeToTransactions(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &EventPublisher_ServiceDesc.Streams[0], EventPublisher_SubscribeToTransactions_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[emptypb.Empty, StreamResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToTransactionsClient = grpc.ServerStreamingClient[StreamResponse]

func (c *eventPublisherClient) SubscribeToAccountUpdates(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &EventPublisher_ServiceDesc.Streams[1], EventPublisher_SubscribeToAccountUpdates_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[emptypb.Empty, StreamResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToAccountUpdatesClient = grpc.ServerStreamingClient[StreamResponse]

func (c *eventPublisherClient) SubscribeToSlotStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &EventPublisher_ServiceDesc.Streams[2], EventPublisher_SubscribeToSlotStatus_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[emptypb.Empty, StreamResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToSlotStatusClient = grpc.ServerStreamingClient[StreamResponse]

func (c *eventPublisherClient) SubscribeToWalletTransactions(ctx context.Context, in *SubscribeWalletRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &EventPublisher_ServiceDesc.Streams[3], EventPublisher_SubscribeToWalletTransactions_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[SubscribeWalletRequest, StreamResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToWalletTransactionsClient = grpc.ServerStreamingClient[StreamResponse]

// EventPublisherServer is the server API for EventPublisher service.
// All implementations must embed UnimplementedEventPublisherServer
// for forward compatibility.
type EventPublisherServer interface {
	SubscribeToTransactions(*emptypb.Empty, grpc.ServerStreamingServer[StreamResponse]) error
	SubscribeToAccountUpdates(*emptypb.Empty, grpc.ServerStreamingServer[StreamResponse]) error
	SubscribeToSlotStatus(*emptypb.Empty, grpc.ServerStreamingServer[StreamResponse]) error
	SubscribeToWalletTransactions(*SubscribeWalletRequest, grpc.ServerStreamingServer[StreamResponse]) error
	mustEmbedUnimplementedEventPublisherServer()
}

// UnimplementedEventPublisherServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEventPublisherServer struct{}

func (UnimplementedEventPublisherServer) SubscribeToTransactions(*emptypb.Empty, grpc.ServerStreamingServer[StreamResponse]) error {
	return status.Errorf(codes.Unimplemented, "method SubscribeToTransactions not implemented")
}
func (UnimplementedEventPublisherServer) SubscribeToAccountUpdates(*emptypb.Empty, grpc.ServerStreamingServer[StreamResponse]) error {
	return status.Errorf(codes.Unimplemented, "method SubscribeToAccountUpdates not implemented")
}
func (UnimplementedEventPublisherServer) SubscribeToSlotStatus(*emptypb.Empty, grpc.ServerStreamingServer[StreamResponse]) error {
	return status.Errorf(codes.Unimplemented, "method SubscribeToSlotStatus not implemented")
}
func (UnimplementedEventPublisherServer) SubscribeToWalletTransactions(*SubscribeWalletRequest, grpc.ServerStreamingServer[StreamResponse]) error {
	return status.Errorf(codes.Unimplemented, "method SubscribeToWalletTransactions not implemented")
}
func (UnimplementedEventPublisherServer) mustEmbedUnimplementedEventPublisherServer() {}
func (UnimplementedEventPublisherServer) testEmbeddedByValue()                        {}

// UnsafeEventPublisherServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EventPublisherServer will
// result in compilation errors.
type UnsafeEventPublisherServer interface {
	mustEmbedUnimplementedEventPublisherServer()
}

func RegisterEventPublisherServer(s grpc.ServiceRegistrar, srv EventPublisherServer) {
	// If the following call pancis, it indicates UnimplementedEventPublisherServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EventPublisher_ServiceDesc, srv)
}

func _EventPublisher_SubscribeToTransactions_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(emptypb.Empty)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(EventPublisherServer).SubscribeToTransactions(m, &grpc.GenericServerStream[emptypb.Empty, StreamResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToTransactionsServer = grpc.ServerStreamingServer[StreamResponse]

func _EventPublisher_SubscribeToAccountUpdates_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(emptypb.Empty)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(EventPublisherServer).SubscribeToAccountUpdates(m, &grpc.GenericServerStream[emptypb.Empty, StreamResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToAccountUpdatesServer = grpc.ServerStreamingServer[StreamResponse]

func _EventPublisher_SubscribeToSlotStatus_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(emptypb.Empty)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(EventPublisherServer).SubscribeToSlotStatus(m, &grpc.GenericServerStream[emptypb.Empty, StreamResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToSlotStatusServer = grpc.ServerStreamingServer[StreamResponse]

func _EventPublisher_SubscribeToWalletTransactions_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SubscribeWalletRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(EventPublisherServer).SubscribeToWalletTransactions(m, &grpc.GenericServerStream[SubscribeWalletRequest, StreamResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EventPublisher_SubscribeToWalletTransactionsServer = grpc.ServerStreamingServer[StreamResponse]

// EventPublisher_ServiceDesc is the grpc.ServiceDesc for EventPublisher service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EventPublisher_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "publisher.EventPublisher",
	HandlerType: (*EventPublisherServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SubscribeToTransactions",
			Handler:       _EventPublisher_SubscribeToTransactions_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "SubscribeToAccountUpdates",
			Handler:       _EventPublisher_SubscribeToAccountUpdates_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "SubscribeToSlotStatus",
			Handler:       _EventPublisher_SubscribeToSlotStatus_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "SubscribeToWalletTransactions",
			Handler:       _EventPublisher_SubscribeToWalletTransactions_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "publisher.proto",
}
