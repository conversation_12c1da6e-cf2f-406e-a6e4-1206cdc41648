// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.28.2
// source: events.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SlotStatus int32

const (
	SlotStatus_PROCESSED SlotStatus = 0 // Most common first
	SlotStatus_CONFIRMED SlotStatus = 1
	SlotStatus_ROOTED    SlotStatus = 2
)

// Enum value maps for SlotStatus.
var (
	SlotStatus_name = map[int32]string{
		0: "PROCESSED",
		1: "CONFIRMED",
		2: "ROOTED",
	}
	SlotStatus_value = map[string]int32{
		"PROCESSED": 0,
		"CONFIRMED": 1,
		"ROOTED":    2,
	}
)

func (x SlotStatus) Enum() *SlotStatus {
	p := new(SlotStatus)
	*p = x
	return p
}

func (x SlotStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SlotStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_events_proto_enumTypes[0].Descriptor()
}

func (SlotStatus) Type() protoreflect.EnumType {
	return &file_events_proto_enumTypes[0]
}

func (x SlotStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SlotStatus.Descriptor instead.
func (SlotStatus) EnumDescriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{0}
}

type StreamType int32

const (
	StreamType_STREAM_TYPE_UNSPECIFIED StreamType = 0
	StreamType_STREAM_TYPE_FILTERED    StreamType = 1
	StreamType_STREAM_TYPE_WALLET      StreamType = 2
)

// Enum value maps for StreamType.
var (
	StreamType_name = map[int32]string{
		0: "STREAM_TYPE_UNSPECIFIED",
		1: "STREAM_TYPE_FILTERED",
		2: "STREAM_TYPE_WALLET",
	}
	StreamType_value = map[string]int32{
		"STREAM_TYPE_UNSPECIFIED": 0,
		"STREAM_TYPE_FILTERED":    1,
		"STREAM_TYPE_WALLET":      2,
	}
)

func (x StreamType) Enum() *StreamType {
	p := new(StreamType)
	*p = x
	return p
}

func (x StreamType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StreamType) Descriptor() protoreflect.EnumDescriptor {
	return file_events_proto_enumTypes[1].Descriptor()
}

func (StreamType) Type() protoreflect.EnumType {
	return &file_events_proto_enumTypes[1]
}

func (x StreamType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StreamType.Descriptor instead.
func (StreamType) EnumDescriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{1}
}

type UpdateAccountEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fixed length fields first
	Slot         uint64 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`                                     // 8 bytes, frequently accessed
	Lamports     uint64 `protobuf:"varint,2,opt,name=lamports,proto3" json:"lamports,omitempty"`                             // 8 bytes, frequently accessed
	RentEpoch    uint64 `protobuf:"varint,3,opt,name=rent_epoch,json=rentEpoch,proto3" json:"rent_epoch,omitempty"`          // 8 bytes
	WriteVersion uint64 `protobuf:"varint,4,opt,name=write_version,json=writeVersion,proto3" json:"write_version,omitempty"` // 8 bytes
	Executable   bool   `protobuf:"varint,5,opt,name=executable,proto3" json:"executable,omitempty"`                         // 1 byte
	// Variable length fields after
	Pubkey       []byte `protobuf:"bytes,6,opt,name=pubkey,proto3" json:"pubkey,omitempty"`                                       // 32 bytes fixed but proto variable
	Owner        []byte `protobuf:"bytes,7,opt,name=owner,proto3" json:"owner,omitempty"`                                         // 32 bytes fixed but proto variable
	Data         []byte `protobuf:"bytes,8,opt,name=data,proto3" json:"data,omitempty"`                                           // variable length
	TxnSignature []byte `protobuf:"bytes,9,opt,name=txn_signature,json=txnSignature,proto3,oneof" json:"txn_signature,omitempty"` // optional, 64 bytes when present
}

func (x *UpdateAccountEvent) Reset() {
	*x = UpdateAccountEvent{}
	mi := &file_events_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAccountEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountEvent) ProtoMessage() {}

func (x *UpdateAccountEvent) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountEvent.ProtoReflect.Descriptor instead.
func (*UpdateAccountEvent) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateAccountEvent) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *UpdateAccountEvent) GetLamports() uint64 {
	if x != nil {
		return x.Lamports
	}
	return 0
}

func (x *UpdateAccountEvent) GetRentEpoch() uint64 {
	if x != nil {
		return x.RentEpoch
	}
	return 0
}

func (x *UpdateAccountEvent) GetWriteVersion() uint64 {
	if x != nil {
		return x.WriteVersion
	}
	return 0
}

func (x *UpdateAccountEvent) GetExecutable() bool {
	if x != nil {
		return x.Executable
	}
	return false
}

func (x *UpdateAccountEvent) GetPubkey() []byte {
	if x != nil {
		return x.Pubkey
	}
	return nil
}

func (x *UpdateAccountEvent) GetOwner() []byte {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *UpdateAccountEvent) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateAccountEvent) GetTxnSignature() []byte {
	if x != nil {
		return x.TxnSignature
	}
	return nil
}

type SlotStatusEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fixed length fields grouped
	Slot   uint64     `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`                                         // 8 bytes, frequently accessed
	Parent uint64     `protobuf:"varint,2,opt,name=parent,proto3" json:"parent,omitempty"`                                     // 8 bytes
	Status SlotStatus `protobuf:"varint,3,opt,name=status,proto3,enum=thor_streamer.types.SlotStatus" json:"status,omitempty"` // enum (4 bytes)
}

func (x *SlotStatusEvent) Reset() {
	*x = SlotStatusEvent{}
	mi := &file_events_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SlotStatusEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotStatusEvent) ProtoMessage() {}

func (x *SlotStatusEvent) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotStatusEvent.ProtoReflect.Descriptor instead.
func (*SlotStatusEvent) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{1}
}

func (x *SlotStatusEvent) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SlotStatusEvent) GetParent() uint64 {
	if x != nil {
		return x.Parent
	}
	return 0
}

func (x *SlotStatusEvent) GetStatus() SlotStatus {
	if x != nil {
		return x.Status
	}
	return SlotStatus_PROCESSED
}

type MessageHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Group small fixed-size fields together
	NumRequiredSignatures       uint32 `protobuf:"varint,1,opt,name=num_required_signatures,json=numRequiredSignatures,proto3" json:"num_required_signatures,omitempty"`
	NumReadonlySignedAccounts   uint32 `protobuf:"varint,2,opt,name=num_readonly_signed_accounts,json=numReadonlySignedAccounts,proto3" json:"num_readonly_signed_accounts,omitempty"`
	NumReadonlyUnsignedAccounts uint32 `protobuf:"varint,3,opt,name=num_readonly_unsigned_accounts,json=numReadonlyUnsignedAccounts,proto3" json:"num_readonly_unsigned_accounts,omitempty"`
}

func (x *MessageHeader) Reset() {
	*x = MessageHeader{}
	mi := &file_events_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageHeader) ProtoMessage() {}

func (x *MessageHeader) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageHeader.ProtoReflect.Descriptor instead.
func (*MessageHeader) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{2}
}

func (x *MessageHeader) GetNumRequiredSignatures() uint32 {
	if x != nil {
		return x.NumRequiredSignatures
	}
	return 0
}

func (x *MessageHeader) GetNumReadonlySignedAccounts() uint32 {
	if x != nil {
		return x.NumReadonlySignedAccounts
	}
	return 0
}

func (x *MessageHeader) GetNumReadonlyUnsignedAccounts() uint32 {
	if x != nil {
		return x.NumReadonlyUnsignedAccounts
	}
	return 0
}

type CompiledInstruction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgramIdIndex uint32   `protobuf:"varint,1,opt,name=program_id_index,json=programIdIndex,proto3" json:"program_id_index,omitempty"`
	Data           []byte   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`                 // Variable length after fixed
	Accounts       []uint32 `protobuf:"varint,3,rep,packed,name=accounts,proto3" json:"accounts,omitempty"` // Use packed for better encoding
}

func (x *CompiledInstruction) Reset() {
	*x = CompiledInstruction{}
	mi := &file_events_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompiledInstruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompiledInstruction) ProtoMessage() {}

func (x *CompiledInstruction) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompiledInstruction.ProtoReflect.Descriptor instead.
func (*CompiledInstruction) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{3}
}

func (x *CompiledInstruction) GetProgramIdIndex() uint32 {
	if x != nil {
		return x.ProgramIdIndex
	}
	return 0
}

func (x *CompiledInstruction) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CompiledInstruction) GetAccounts() []uint32 {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type LoadedAddresses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Writable [][]byte `protobuf:"bytes,1,rep,name=writable,proto3" json:"writable,omitempty"`
	Readonly [][]byte `protobuf:"bytes,2,rep,name=readonly,proto3" json:"readonly,omitempty"`
}

func (x *LoadedAddresses) Reset() {
	*x = LoadedAddresses{}
	mi := &file_events_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadedAddresses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadedAddresses) ProtoMessage() {}

func (x *LoadedAddresses) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadedAddresses.ProtoReflect.Descriptor instead.
func (*LoadedAddresses) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{4}
}

func (x *LoadedAddresses) GetWritable() [][]byte {
	if x != nil {
		return x.Writable
	}
	return nil
}

func (x *LoadedAddresses) GetReadonly() [][]byte {
	if x != nil {
		return x.Readonly
	}
	return nil
}

type MessageAddressTableLookup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountKey      []byte `protobuf:"bytes,1,opt,name=account_key,json=accountKey,proto3" json:"account_key,omitempty"`                // 32 bytes
	WritableIndexes []byte `protobuf:"bytes,2,opt,name=writable_indexes,json=writableIndexes,proto3" json:"writable_indexes,omitempty"` // 32 bytes
	ReadonlyIndexes []byte `protobuf:"bytes,3,opt,name=readonly_indexes,json=readonlyIndexes,proto3" json:"readonly_indexes,omitempty"` // 32 bytes
}

func (x *MessageAddressTableLookup) Reset() {
	*x = MessageAddressTableLookup{}
	mi := &file_events_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageAddressTableLookup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageAddressTableLookup) ProtoMessage() {}

func (x *MessageAddressTableLookup) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageAddressTableLookup.ProtoReflect.Descriptor instead.
func (*MessageAddressTableLookup) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{5}
}

func (x *MessageAddressTableLookup) GetAccountKey() []byte {
	if x != nil {
		return x.AccountKey
	}
	return nil
}

func (x *MessageAddressTableLookup) GetWritableIndexes() []byte {
	if x != nil {
		return x.WritableIndexes
	}
	return nil
}

func (x *MessageAddressTableLookup) GetReadonlyIndexes() []byte {
	if x != nil {
		return x.ReadonlyIndexes
	}
	return nil
}

// Unified Message structure that handles both Legacy and v0
type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version             uint32                       `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"` // 0 for legacy, 1 for v0
	Header              *MessageHeader               `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	RecentBlockHash     []byte                       `protobuf:"bytes,3,opt,name=recent_block_hash,json=recentBlockHash,proto3" json:"recent_block_hash,omitempty"` // 32 bytes
	AccountKeys         [][]byte                     `protobuf:"bytes,4,rep,name=account_keys,json=accountKeys,proto3" json:"account_keys,omitempty"`               // Array of 32 byte keys
	Instructions        []*CompiledInstruction       `protobuf:"bytes,5,rep,name=instructions,proto3" json:"instructions,omitempty"`
	AddressTableLookups []*MessageAddressTableLookup `protobuf:"bytes,6,rep,name=address_table_lookups,json=addressTableLookups,proto3" json:"address_table_lookups,omitempty"` // Only used for v0
	LoadedAddresses     *LoadedAddresses             `protobuf:"bytes,7,opt,name=loaded_addresses,json=loadedAddresses,proto3" json:"loaded_addresses,omitempty"`               // Only used for v0
	IsWritable          []bool                       `protobuf:"varint,8,rep,packed,name=is_writable,json=isWritable,proto3" json:"is_writable,omitempty"`                      // Account write permissions
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_events_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{6}
}

func (x *Message) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Message) GetHeader() *MessageHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *Message) GetRecentBlockHash() []byte {
	if x != nil {
		return x.RecentBlockHash
	}
	return nil
}

func (x *Message) GetAccountKeys() [][]byte {
	if x != nil {
		return x.AccountKeys
	}
	return nil
}

func (x *Message) GetInstructions() []*CompiledInstruction {
	if x != nil {
		return x.Instructions
	}
	return nil
}

func (x *Message) GetAddressTableLookups() []*MessageAddressTableLookup {
	if x != nil {
		return x.AddressTableLookups
	}
	return nil
}

func (x *Message) GetLoadedAddresses() *LoadedAddresses {
	if x != nil {
		return x.LoadedAddresses
	}
	return nil
}

func (x *Message) GetIsWritable() []bool {
	if x != nil {
		return x.IsWritable
	}
	return nil
}

type SanitizedTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message                 *Message `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	MessageHash             []byte   `protobuf:"bytes,2,opt,name=message_hash,json=messageHash,proto3" json:"message_hash,omitempty"` // 32 bytes
	Signatures              [][]byte `protobuf:"bytes,3,rep,name=signatures,proto3" json:"signatures,omitempty"`                      // Array of 64 byte signatures
	IsSimpleVoteTransaction bool     `protobuf:"varint,4,opt,name=is_simple_vote_transaction,json=isSimpleVoteTransaction,proto3" json:"is_simple_vote_transaction,omitempty"`
}

func (x *SanitizedTransaction) Reset() {
	*x = SanitizedTransaction{}
	mi := &file_events_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SanitizedTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SanitizedTransaction) ProtoMessage() {}

func (x *SanitizedTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SanitizedTransaction.ProtoReflect.Descriptor instead.
func (*SanitizedTransaction) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{7}
}

func (x *SanitizedTransaction) GetMessage() *Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *SanitizedTransaction) GetMessageHash() []byte {
	if x != nil {
		return x.MessageHash
	}
	return nil
}

func (x *SanitizedTransaction) GetSignatures() [][]byte {
	if x != nil {
		return x.Signatures
	}
	return nil
}

func (x *SanitizedTransaction) GetIsSimpleVoteTransaction() bool {
	if x != nil {
		return x.IsSimpleVoteTransaction
	}
	return false
}

type TransactionEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot                  uint64                 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Signature             []byte                 `protobuf:"bytes,2,opt,name=signature,proto3" json:"signature,omitempty"` // 64 bytes
	Index                 uint64                 `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	IsVote                bool                   `protobuf:"varint,4,opt,name=is_vote,json=isVote,proto3" json:"is_vote,omitempty"`
	Transaction           *SanitizedTransaction  `protobuf:"bytes,5,opt,name=transaction,proto3" json:"transaction,omitempty"`
	TransactionStatusMeta *TransactionStatusMeta `protobuf:"bytes,6,opt,name=transaction_status_meta,json=transactionStatusMeta,proto3" json:"transaction_status_meta,omitempty"`
}

func (x *TransactionEvent) Reset() {
	*x = TransactionEvent{}
	mi := &file_events_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionEvent) ProtoMessage() {}

func (x *TransactionEvent) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionEvent.ProtoReflect.Descriptor instead.
func (*TransactionEvent) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{8}
}

func (x *TransactionEvent) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *TransactionEvent) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *TransactionEvent) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *TransactionEvent) GetIsVote() bool {
	if x != nil {
		return x.IsVote
	}
	return false
}

func (x *TransactionEvent) GetTransaction() *SanitizedTransaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *TransactionEvent) GetTransactionStatusMeta() *TransactionStatusMeta {
	if x != nil {
		return x.TransactionStatusMeta
	}
	return nil
}

type InnerInstruction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instruction *CompiledInstruction `protobuf:"bytes,1,opt,name=instruction,proto3" json:"instruction,omitempty"`
	StackHeight *uint32              `protobuf:"varint,2,opt,name=stack_height,json=stackHeight,proto3,oneof" json:"stack_height,omitempty"`
}

func (x *InnerInstruction) Reset() {
	*x = InnerInstruction{}
	mi := &file_events_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InnerInstruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InnerInstruction) ProtoMessage() {}

func (x *InnerInstruction) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InnerInstruction.ProtoReflect.Descriptor instead.
func (*InnerInstruction) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{9}
}

func (x *InnerInstruction) GetInstruction() *CompiledInstruction {
	if x != nil {
		return x.Instruction
	}
	return nil
}

func (x *InnerInstruction) GetStackHeight() uint32 {
	if x != nil && x.StackHeight != nil {
		return *x.StackHeight
	}
	return 0
}

type InnerInstructions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index        uint32              `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Instructions []*InnerInstruction `protobuf:"bytes,2,rep,name=instructions,proto3" json:"instructions,omitempty"`
}

func (x *InnerInstructions) Reset() {
	*x = InnerInstructions{}
	mi := &file_events_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InnerInstructions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InnerInstructions) ProtoMessage() {}

func (x *InnerInstructions) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InnerInstructions.ProtoReflect.Descriptor instead.
func (*InnerInstructions) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{10}
}

func (x *InnerInstructions) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *InnerInstructions) GetInstructions() []*InnerInstruction {
	if x != nil {
		return x.Instructions
	}
	return nil
}

type UiTokenAmount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UiAmount       float64 `protobuf:"fixed64,1,opt,name=ui_amount,json=uiAmount,proto3" json:"ui_amount,omitempty"`
	Decimals       uint32  `protobuf:"varint,2,opt,name=decimals,proto3" json:"decimals,omitempty"`
	Amount         string  `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	UiAmountString string  `protobuf:"bytes,4,opt,name=ui_amount_string,json=uiAmountString,proto3" json:"ui_amount_string,omitempty"`
}

func (x *UiTokenAmount) Reset() {
	*x = UiTokenAmount{}
	mi := &file_events_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UiTokenAmount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UiTokenAmount) ProtoMessage() {}

func (x *UiTokenAmount) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UiTokenAmount.ProtoReflect.Descriptor instead.
func (*UiTokenAmount) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{11}
}

func (x *UiTokenAmount) GetUiAmount() float64 {
	if x != nil {
		return x.UiAmount
	}
	return 0
}

func (x *UiTokenAmount) GetDecimals() uint32 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *UiTokenAmount) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *UiTokenAmount) GetUiAmountString() string {
	if x != nil {
		return x.UiAmountString
	}
	return ""
}

type TransactionTokenBalance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountIndex  uint32         `protobuf:"varint,1,opt,name=account_index,json=accountIndex,proto3" json:"account_index,omitempty"`
	Mint          string         `protobuf:"bytes,2,opt,name=mint,proto3" json:"mint,omitempty"`
	UiTokenAmount *UiTokenAmount `protobuf:"bytes,3,opt,name=ui_token_amount,json=uiTokenAmount,proto3" json:"ui_token_amount,omitempty"`
	Owner         string         `protobuf:"bytes,4,opt,name=owner,proto3" json:"owner,omitempty"`
}

func (x *TransactionTokenBalance) Reset() {
	*x = TransactionTokenBalance{}
	mi := &file_events_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionTokenBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionTokenBalance) ProtoMessage() {}

func (x *TransactionTokenBalance) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionTokenBalance.ProtoReflect.Descriptor instead.
func (*TransactionTokenBalance) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{12}
}

func (x *TransactionTokenBalance) GetAccountIndex() uint32 {
	if x != nil {
		return x.AccountIndex
	}
	return 0
}

func (x *TransactionTokenBalance) GetMint() string {
	if x != nil {
		return x.Mint
	}
	return ""
}

func (x *TransactionTokenBalance) GetUiTokenAmount() *UiTokenAmount {
	if x != nil {
		return x.UiTokenAmount
	}
	return nil
}

func (x *TransactionTokenBalance) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pubkey      string `protobuf:"bytes,1,opt,name=pubkey,proto3" json:"pubkey,omitempty"`
	Lamports    int64  `protobuf:"varint,2,opt,name=lamports,proto3" json:"lamports,omitempty"`
	PostBalance uint64 `protobuf:"varint,3,opt,name=post_balance,json=postBalance,proto3" json:"post_balance,omitempty"`
	RewardType  int32  `protobuf:"varint,4,opt,name=reward_type,json=rewardType,proto3" json:"reward_type,omitempty"`
	Commission  uint32 `protobuf:"varint,5,opt,name=commission,proto3" json:"commission,omitempty"`
}

func (x *Reward) Reset() {
	*x = Reward{}
	mi := &file_events_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{13}
}

func (x *Reward) GetPubkey() string {
	if x != nil {
		return x.Pubkey
	}
	return ""
}

func (x *Reward) GetLamports() int64 {
	if x != nil {
		return x.Lamports
	}
	return 0
}

func (x *Reward) GetPostBalance() uint64 {
	if x != nil {
		return x.PostBalance
	}
	return 0
}

func (x *Reward) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *Reward) GetCommission() uint32 {
	if x != nil {
		return x.Commission
	}
	return 0
}

type TransactionStatusMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Most frequently accessed fields first
	IsStatusErr  bool     `protobuf:"varint,1,opt,name=is_status_err,json=isStatusErr,proto3" json:"is_status_err,omitempty"`
	Fee          uint64   `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	PreBalances  []uint64 `protobuf:"varint,3,rep,packed,name=pre_balances,json=preBalances,proto3" json:"pre_balances,omitempty"`
	PostBalances []uint64 `protobuf:"varint,4,rep,packed,name=post_balances,json=postBalances,proto3" json:"post_balances,omitempty"`
	// Larger and optional fields after
	InnerInstructions []*InnerInstructions       `protobuf:"bytes,5,rep,name=inner_instructions,json=innerInstructions,proto3" json:"inner_instructions,omitempty"`
	LogMessages       []string                   `protobuf:"bytes,6,rep,name=log_messages,json=logMessages,proto3" json:"log_messages,omitempty"`
	PreTokenBalances  []*TransactionTokenBalance `protobuf:"bytes,7,rep,name=pre_token_balances,json=preTokenBalances,proto3" json:"pre_token_balances,omitempty"`
	PostTokenBalances []*TransactionTokenBalance `protobuf:"bytes,8,rep,name=post_token_balances,json=postTokenBalances,proto3" json:"post_token_balances,omitempty"`
	Rewards           []*Reward                  `protobuf:"bytes,9,rep,name=rewards,proto3" json:"rewards,omitempty"`
	ErrorInfo         string                     `protobuf:"bytes,10,opt,name=error_info,json=errorInfo,proto3" json:"error_info,omitempty"`
}

func (x *TransactionStatusMeta) Reset() {
	*x = TransactionStatusMeta{}
	mi := &file_events_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionStatusMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionStatusMeta) ProtoMessage() {}

func (x *TransactionStatusMeta) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionStatusMeta.ProtoReflect.Descriptor instead.
func (*TransactionStatusMeta) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{14}
}

func (x *TransactionStatusMeta) GetIsStatusErr() bool {
	if x != nil {
		return x.IsStatusErr
	}
	return false
}

func (x *TransactionStatusMeta) GetFee() uint64 {
	if x != nil {
		return x.Fee
	}
	return 0
}

func (x *TransactionStatusMeta) GetPreBalances() []uint64 {
	if x != nil {
		return x.PreBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetPostBalances() []uint64 {
	if x != nil {
		return x.PostBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetInnerInstructions() []*InnerInstructions {
	if x != nil {
		return x.InnerInstructions
	}
	return nil
}

func (x *TransactionStatusMeta) GetLogMessages() []string {
	if x != nil {
		return x.LogMessages
	}
	return nil
}

func (x *TransactionStatusMeta) GetPreTokenBalances() []*TransactionTokenBalance {
	if x != nil {
		return x.PreTokenBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetPostTokenBalances() []*TransactionTokenBalance {
	if x != nil {
		return x.PostTokenBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *TransactionStatusMeta) GetErrorInfo() string {
	if x != nil {
		return x.ErrorInfo
	}
	return ""
}

type TransactionEventWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StreamType  StreamType        `protobuf:"varint,1,opt,name=stream_type,json=streamType,proto3,enum=thor_streamer.types.StreamType" json:"stream_type,omitempty"`
	Transaction *TransactionEvent `protobuf:"bytes,2,opt,name=transaction,proto3" json:"transaction,omitempty"`
}

func (x *TransactionEventWrapper) Reset() {
	*x = TransactionEventWrapper{}
	mi := &file_events_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionEventWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionEventWrapper) ProtoMessage() {}

func (x *TransactionEventWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionEventWrapper.ProtoReflect.Descriptor instead.
func (*TransactionEventWrapper) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{15}
}

func (x *TransactionEventWrapper) GetStreamType() StreamType {
	if x != nil {
		return x.StreamType
	}
	return StreamType_STREAM_TYPE_UNSPECIFIED
}

func (x *TransactionEventWrapper) GetTransaction() *TransactionEvent {
	if x != nil {
		return x.Transaction
	}
	return nil
}

type MessageWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to EventMessage:
	//
	//	*MessageWrapper_Account
	//	*MessageWrapper_Slot
	//	*MessageWrapper_Transaction
	EventMessage isMessageWrapper_EventMessage `protobuf_oneof:"event_message"`
}

func (x *MessageWrapper) Reset() {
	*x = MessageWrapper{}
	mi := &file_events_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageWrapper) ProtoMessage() {}

func (x *MessageWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_events_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageWrapper.ProtoReflect.Descriptor instead.
func (*MessageWrapper) Descriptor() ([]byte, []int) {
	return file_events_proto_rawDescGZIP(), []int{16}
}

func (m *MessageWrapper) GetEventMessage() isMessageWrapper_EventMessage {
	if m != nil {
		return m.EventMessage
	}
	return nil
}

func (x *MessageWrapper) GetAccount() *UpdateAccountEvent {
	if x, ok := x.GetEventMessage().(*MessageWrapper_Account); ok {
		return x.Account
	}
	return nil
}

func (x *MessageWrapper) GetSlot() *SlotStatusEvent {
	if x, ok := x.GetEventMessage().(*MessageWrapper_Slot); ok {
		return x.Slot
	}
	return nil
}

func (x *MessageWrapper) GetTransaction() *TransactionEventWrapper {
	if x, ok := x.GetEventMessage().(*MessageWrapper_Transaction); ok {
		return x.Transaction
	}
	return nil
}

type isMessageWrapper_EventMessage interface {
	isMessageWrapper_EventMessage()
}

type MessageWrapper_Account struct {
	Account *UpdateAccountEvent `protobuf:"bytes,1,opt,name=account,proto3,oneof"`
}

type MessageWrapper_Slot struct {
	Slot *SlotStatusEvent `protobuf:"bytes,2,opt,name=slot,proto3,oneof"`
}

type MessageWrapper_Transaction struct {
	Transaction *TransactionEventWrapper `protobuf:"bytes,3,opt,name=transaction,proto3,oneof"`
}

func (*MessageWrapper_Account) isMessageWrapper_EventMessage() {}

func (*MessageWrapper_Slot) isMessageWrapper_EventMessage() {}

func (*MessageWrapper_Transaction) isMessageWrapper_EventMessage() {}

var File_events_proto protoreflect.FileDescriptor

var file_events_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13,
	0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x22, 0xa6, 0x02, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c,
	0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x72, 0x65, 0x6e, 0x74, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06,
	0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x28, 0x0a, 0x0d, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x78, 0x6e, 0x53, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x74,
	0x78, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x76, 0x0a, 0x0f,
	0x53, 0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73,
	0x6c, 0x6f, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x68,
	0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xcd, 0x01, 0x0a, 0x0d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x17, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x3f,
	0x0a, 0x1c, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c,
	0x79, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12,
	0x43, 0x0a, 0x1e, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x5f,
	0x75, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1b, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x61, 0x64,
	0x6f, 0x6e, 0x6c, 0x79, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x22, 0x73, 0x0a, 0x13, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x65, 0x64,
	0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x08, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x49, 0x0a, 0x0f, 0x4c, 0x6f, 0x61,
	0x64, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x77, 0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x08,
	0x77, 0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x64,
	0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64,
	0x6f, 0x6e, 0x6c, 0x79, 0x22, 0x92, 0x01, 0x0a, 0x19, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x6f, 0x6b,
	0x75, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x77, 0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x77,
	0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x12, 0x29,
	0x0a, 0x10, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e,
	0x6c, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x22, 0xd6, 0x03, 0x0a, 0x07, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x72,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x4c, 0x0a, 0x0c, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x65, 0x64, 0x49,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x62, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x13, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x73, 0x12, 0x4f, 0x0a, 0x10,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4c, 0x6f, 0x61,
	0x64, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x0f, 0x6c, 0x6f,
	0x61, 0x64, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x23, 0x0a,
	0x0b, 0x69, 0x73, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x08, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x69, 0x73, 0x57, 0x72, 0x69, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x14, 0x53, 0x61, 0x6e, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74,
	0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x73, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x5f, 0x76, 0x6f, 0x74, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xa4, 0x02, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x76, 0x6f, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x69, 0x73, 0x56, 0x6f, 0x74, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x53, 0x61, 0x6e, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x17, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x97, 0x01, 0x0a, 0x10, 0x49,
	0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4a, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x69,
	0x6c, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0c, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x22, 0x74, 0x0a, 0x11, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x49, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x6e, 0x65,
	0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x0d, 0x55,
	0x69, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x75, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x08, 0x75, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63,
	0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x65, 0x63,
	0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a,
	0x10, 0x75, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0xb4, 0x01, 0x0a, 0x17, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x69, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0f,
	0x75, 0x69, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x55, 0x69, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0d, 0x75, 0x69, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0xa0,
	0x01, 0x0a, 0x06, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x75, 0x62,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0xa7, 0x04, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x69,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x72, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x66, 0x65,
	0x65, 0x12, 0x25, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x72, 0x65,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x74,
	0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x42,
	0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x12, 0x55, 0x0a, 0x12, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x67, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x6c, 0x6f, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x5a, 0x0a, 0x12, 0x70,
	0x72, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x10, 0x70, 0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x13, 0x70, 0x6f, 0x73, 0x74, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x11, 0x70, 0x6f, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa4, 0x01, 0x0a, 0x17,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74,
	0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xf4, 0x01, 0x0a, 0x0e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x57, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x73, 0x6c,
	0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x68, 0x6f, 0x72, 0x5f,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x53,
	0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00,
	0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x50, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x68,
	0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x36, 0x0a, 0x0a, 0x53, 0x6c, 0x6f,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52,
	0x4d, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x4f, 0x4f, 0x54, 0x45, 0x44, 0x10,
	0x02, 0x2a, 0x5b, 0x0a, 0x0a, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1b, 0x0a, 0x17, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x54,
	0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x10, 0x02, 0x42, 0x1b,
	0x5a, 0x19, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_events_proto_rawDescOnce sync.Once
	file_events_proto_rawDescData = file_events_proto_rawDesc
)

func file_events_proto_rawDescGZIP() []byte {
	file_events_proto_rawDescOnce.Do(func() {
		file_events_proto_rawDescData = protoimpl.X.CompressGZIP(file_events_proto_rawDescData)
	})
	return file_events_proto_rawDescData
}

var file_events_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_events_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_events_proto_goTypes = []any{
	(SlotStatus)(0),                   // 0: thor_streamer.types.SlotStatus
	(StreamType)(0),                   // 1: thor_streamer.types.StreamType
	(*UpdateAccountEvent)(nil),        // 2: thor_streamer.types.UpdateAccountEvent
	(*SlotStatusEvent)(nil),           // 3: thor_streamer.types.SlotStatusEvent
	(*MessageHeader)(nil),             // 4: thor_streamer.types.MessageHeader
	(*CompiledInstruction)(nil),       // 5: thor_streamer.types.CompiledInstruction
	(*LoadedAddresses)(nil),           // 6: thor_streamer.types.LoadedAddresses
	(*MessageAddressTableLookup)(nil), // 7: thor_streamer.types.MessageAddressTableLookup
	(*Message)(nil),                   // 8: thor_streamer.types.Message
	(*SanitizedTransaction)(nil),      // 9: thor_streamer.types.SanitizedTransaction
	(*TransactionEvent)(nil),          // 10: thor_streamer.types.TransactionEvent
	(*InnerInstruction)(nil),          // 11: thor_streamer.types.InnerInstruction
	(*InnerInstructions)(nil),         // 12: thor_streamer.types.InnerInstructions
	(*UiTokenAmount)(nil),             // 13: thor_streamer.types.UiTokenAmount
	(*TransactionTokenBalance)(nil),   // 14: thor_streamer.types.TransactionTokenBalance
	(*Reward)(nil),                    // 15: thor_streamer.types.Reward
	(*TransactionStatusMeta)(nil),     // 16: thor_streamer.types.TransactionStatusMeta
	(*TransactionEventWrapper)(nil),   // 17: thor_streamer.types.TransactionEventWrapper
	(*MessageWrapper)(nil),            // 18: thor_streamer.types.MessageWrapper
}
var file_events_proto_depIdxs = []int32{
	0,  // 0: thor_streamer.types.SlotStatusEvent.status:type_name -> thor_streamer.types.SlotStatus
	4,  // 1: thor_streamer.types.Message.header:type_name -> thor_streamer.types.MessageHeader
	5,  // 2: thor_streamer.types.Message.instructions:type_name -> thor_streamer.types.CompiledInstruction
	7,  // 3: thor_streamer.types.Message.address_table_lookups:type_name -> thor_streamer.types.MessageAddressTableLookup
	6,  // 4: thor_streamer.types.Message.loaded_addresses:type_name -> thor_streamer.types.LoadedAddresses
	8,  // 5: thor_streamer.types.SanitizedTransaction.message:type_name -> thor_streamer.types.Message
	9,  // 6: thor_streamer.types.TransactionEvent.transaction:type_name -> thor_streamer.types.SanitizedTransaction
	16, // 7: thor_streamer.types.TransactionEvent.transaction_status_meta:type_name -> thor_streamer.types.TransactionStatusMeta
	5,  // 8: thor_streamer.types.InnerInstruction.instruction:type_name -> thor_streamer.types.CompiledInstruction
	11, // 9: thor_streamer.types.InnerInstructions.instructions:type_name -> thor_streamer.types.InnerInstruction
	13, // 10: thor_streamer.types.TransactionTokenBalance.ui_token_amount:type_name -> thor_streamer.types.UiTokenAmount
	12, // 11: thor_streamer.types.TransactionStatusMeta.inner_instructions:type_name -> thor_streamer.types.InnerInstructions
	14, // 12: thor_streamer.types.TransactionStatusMeta.pre_token_balances:type_name -> thor_streamer.types.TransactionTokenBalance
	14, // 13: thor_streamer.types.TransactionStatusMeta.post_token_balances:type_name -> thor_streamer.types.TransactionTokenBalance
	15, // 14: thor_streamer.types.TransactionStatusMeta.rewards:type_name -> thor_streamer.types.Reward
	1,  // 15: thor_streamer.types.TransactionEventWrapper.stream_type:type_name -> thor_streamer.types.StreamType
	10, // 16: thor_streamer.types.TransactionEventWrapper.transaction:type_name -> thor_streamer.types.TransactionEvent
	2,  // 17: thor_streamer.types.MessageWrapper.account:type_name -> thor_streamer.types.UpdateAccountEvent
	3,  // 18: thor_streamer.types.MessageWrapper.slot:type_name -> thor_streamer.types.SlotStatusEvent
	17, // 19: thor_streamer.types.MessageWrapper.transaction:type_name -> thor_streamer.types.TransactionEventWrapper
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_events_proto_init() }
func file_events_proto_init() {
	if File_events_proto != nil {
		return
	}
	file_events_proto_msgTypes[0].OneofWrappers = []any{}
	file_events_proto_msgTypes[9].OneofWrappers = []any{}
	file_events_proto_msgTypes[16].OneofWrappers = []any{
		(*MessageWrapper_Account)(nil),
		(*MessageWrapper_Slot)(nil),
		(*MessageWrapper_Transaction)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_events_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_events_proto_goTypes,
		DependencyIndexes: file_events_proto_depIdxs,
		EnumInfos:         file_events_proto_enumTypes,
		MessageInfos:      file_events_proto_msgTypes,
	}.Build()
	File_events_proto = out.File
	file_events_proto_rawDesc = nil
	file_events_proto_goTypes = nil
	file_events_proto_depIdxs = nil
}
