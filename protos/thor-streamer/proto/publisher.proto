syntax = "proto3";

package publisher;

import "google/protobuf/empty.proto";

option go_package = "thor_streamer/proto";

message StreamResponse {
  bytes data = 1;
}

service EventPublisher {
  rpc SubscribeToTransactions(google.protobuf.Empty) returns (stream StreamResponse) {}
  rpc SubscribeToAccountUpdates(google.protobuf.Empty) returns (stream StreamResponse) {}
  rpc SubscribeToSlotStatus(google.protobuf.Empty) returns (stream StreamResponse) {}
  rpc SubscribeToWalletTransactions(SubscribeWalletRequest) returns (stream StreamResponse) {}
}

message SubscribeWalletRequest {
  repeated string wallet_address = 1; // Array of Base58 encoded wallet addresses, max 10
}
