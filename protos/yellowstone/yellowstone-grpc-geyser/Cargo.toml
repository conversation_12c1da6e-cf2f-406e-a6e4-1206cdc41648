[package]
name = "yellowstone-grpc-geyser"
version = "6.1.0"
authors = { workspace = true }
edition = { workspace = true }
description = "Yellowstone gRPC Geyser Plugin"
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
keywords = { workspace = true }
publish = { workspace = true }

[lib]
crate-type = ["cdylib", "rlib"]

[[bin]]
name = "config-check"

[dependencies]
affinity = { workspace = true }
agave-geyser-plugin-interface = { workspace = true }
anyhow = { workspace = true }
base64 = { workspace = true }
bincode = { workspace = true }
bs58 = { workspace = true }
clap = { workspace = true, features = ["derive"] }
crossbeam-channel = { workspace = true }
futures = { workspace = true }
hostname = { workspace = true }
http = { workspace = true }
http-body-util = { workspace = true }
humantime-serde = { workspace = true }
hyper = { workspace = true }
hyper-util = { workspace = true }
lazy_static = { workspace = true }
log = { workspace = true }
prometheus = { workspace = true }
prost-types = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
solana-logger = { workspace = true }
solana-sdk = { workspace = true }
solana-transaction-status = { workspace = true }
spl-token-2022 = { workspace = true, features = ["no-entrypoint"] }
thiserror = { workspace = true }
tokio = { workspace = true, features = ["rt-multi-thread", "macros", "fs"] }
tokio-stream = { workspace = true }
tonic = { workspace = true, features = ["gzip", "zstd", "tls", "tls-roots"] }
tonic-health = { workspace = true }
yellowstone-grpc-proto = { workspace = true, features = ["convert", "plugin"] }

[build-dependencies]
anyhow = { workspace = true }
cargo-lock = { workspace = true }
git-version = { workspace = true }
vergen = { workspace = true, features = ["build", "rustc"] }

[lints]
workspace = true
