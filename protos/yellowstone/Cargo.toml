[workspace]
resolver = "2"
members = [
    "examples/rust", # 6.1.0
    "yellowstone-grpc-client", # 6.1.0
    "yellowstone-grpc-geyser", # 6.1.0
    "yellowstone-grpc-proto", # 6.1.0
]
exclude = [
    "yellowstone-grpc-client-nodejs/solana-encoding-wasm", # 3.0.0
]

[workspace.package]
authors = ["Triton One"]
edition = "2021"
homepage = "https://triton.one"
repository = "https://github.com/rpcpool/yellowstone-grpc"
license = "AGPL-3.0"
keywords = ["solana"]
publish = false

[workspace.dependencies]
affinity = "0.1.2"
agave-geyser-plugin-interface = "~2.2.1"
anyhow = "1.0.62"
backoff = "0.4.0"
base64 = "0.22.1"
bincode = "1.3.3"
bs58 = "0.5.1"
bytes = "1.3.0"
cargo-lock = "10.0.1"
chrono = "0.4.26"
clap = "4.3.0"
criterion = "0.5.1"
crossbeam-channel = "0.5.8"
env_logger = "0.11.3"
futures = "0.3.24"
git-version = "0.3.5"
hex = "0.4.3"
hostname = "0.4.0"
http = "1.1.0"
http-body-util = "0.1.2"
humantime-serde = "1.1.1"
hyper = "1.4.1"
hyper-util = "0.1.7"
indicatif = "0.17.9"
lazy_static = "1.4.0"
local-ip-address = "0.6.1"
log = "0.4.17"
maplit = "1.0.2"
prometheus = "0.13.2"
prost = "0.13.1"
prost-types = "0.13.3"
prost_011 = { package = "prost", version = "0.11.9" }
protobuf-src = "1.1.0"
serde = "1.0.145"
serde_json = "1.0.86"
solana-account-decoder = "~2.2.1"
solana-logger = "~2.2.1"
solana-sdk = "~2.2.1"
solana-storage-proto = "~2.2.1"
solana-transaction-status = "~2.2.1"
smallvec = "1.13.2"
spl-token-2022 = "6.0.0"
thiserror = "1.0.63"
tokio = "1.21.2"
tokio-stream = "0.1.11"
tonic = "0.12.1"
tonic-build = "0.12.1"
tonic-health = "0.12.1"
vergen = "9.0.0"
yellowstone-grpc-client = { path = "yellowstone-grpc-client", version = "6.1.0" }
yellowstone-grpc-proto = { path = "yellowstone-grpc-proto", version = "6.1.0", default-features = false }

[workspace.lints.clippy]
clone_on_ref_ptr = "deny"
missing_const_for_fn = "deny"
trivially_copy_pass_by_ref = "deny"

[profile.release]
lto = true
codegen-units = 1
