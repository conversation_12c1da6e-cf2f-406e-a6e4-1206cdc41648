// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v3.19.6
// source: geyser.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommitmentLevel int32

const (
	CommitmentLevel_PROCESSED CommitmentLevel = 0
	CommitmentLevel_CONFIRMED CommitmentLevel = 1
	CommitmentLevel_FINALIZED CommitmentLevel = 2
)

// Enum value maps for CommitmentLevel.
var (
	CommitmentLevel_name = map[int32]string{
		0: "PROCESSED",
		1: "CONFIRMED",
		2: "FINALIZED",
	}
	CommitmentLevel_value = map[string]int32{
		"PROCESSED": 0,
		"CONFIRMED": 1,
		"FINALIZED": 2,
	}
)

func (x CommitmentLevel) Enum() *CommitmentLevel {
	p := new(CommitmentLevel)
	*p = x
	return p
}

func (x CommitmentLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommitmentLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_geyser_proto_enumTypes[0].Descriptor()
}

func (CommitmentLevel) Type() protoreflect.EnumType {
	return &file_geyser_proto_enumTypes[0]
}

func (x CommitmentLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommitmentLevel.Descriptor instead.
func (CommitmentLevel) EnumDescriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{0}
}

type SlotStatus int32

const (
	SlotStatus_SLOT_PROCESSED            SlotStatus = 0
	SlotStatus_SLOT_CONFIRMED            SlotStatus = 1
	SlotStatus_SLOT_FINALIZED            SlotStatus = 2
	SlotStatus_SLOT_FIRST_SHRED_RECEIVED SlotStatus = 3
	SlotStatus_SLOT_COMPLETED            SlotStatus = 4
	SlotStatus_SLOT_CREATED_BANK         SlotStatus = 5
	SlotStatus_SLOT_DEAD                 SlotStatus = 6
)

// Enum value maps for SlotStatus.
var (
	SlotStatus_name = map[int32]string{
		0: "SLOT_PROCESSED",
		1: "SLOT_CONFIRMED",
		2: "SLOT_FINALIZED",
		3: "SLOT_FIRST_SHRED_RECEIVED",
		4: "SLOT_COMPLETED",
		5: "SLOT_CREATED_BANK",
		6: "SLOT_DEAD",
	}
	SlotStatus_value = map[string]int32{
		"SLOT_PROCESSED":            0,
		"SLOT_CONFIRMED":            1,
		"SLOT_FINALIZED":            2,
		"SLOT_FIRST_SHRED_RECEIVED": 3,
		"SLOT_COMPLETED":            4,
		"SLOT_CREATED_BANK":         5,
		"SLOT_DEAD":                 6,
	}
)

func (x SlotStatus) Enum() *SlotStatus {
	p := new(SlotStatus)
	*p = x
	return p
}

func (x SlotStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SlotStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_geyser_proto_enumTypes[1].Descriptor()
}

func (SlotStatus) Type() protoreflect.EnumType {
	return &file_geyser_proto_enumTypes[1]
}

func (x SlotStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SlotStatus.Descriptor instead.
func (SlotStatus) EnumDescriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{1}
}

type SubscribeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Accounts           map[string]*SubscribeRequestFilterAccounts     `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Slots              map[string]*SubscribeRequestFilterSlots        `protobuf:"bytes,2,rep,name=slots,proto3" json:"slots,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Transactions       map[string]*SubscribeRequestFilterTransactions `protobuf:"bytes,3,rep,name=transactions,proto3" json:"transactions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TransactionsStatus map[string]*SubscribeRequestFilterTransactions `protobuf:"bytes,10,rep,name=transactions_status,json=transactionsStatus,proto3" json:"transactions_status,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Blocks             map[string]*SubscribeRequestFilterBlocks       `protobuf:"bytes,4,rep,name=blocks,proto3" json:"blocks,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	BlocksMeta         map[string]*SubscribeRequestFilterBlocksMeta   `protobuf:"bytes,5,rep,name=blocks_meta,json=blocksMeta,proto3" json:"blocks_meta,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Entry              map[string]*SubscribeRequestFilterEntry        `protobuf:"bytes,8,rep,name=entry,proto3" json:"entry,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Commitment         *CommitmentLevel                               `protobuf:"varint,6,opt,name=commitment,proto3,enum=geyser.CommitmentLevel,oneof" json:"commitment,omitempty"`
	AccountsDataSlice  []*SubscribeRequestAccountsDataSlice           `protobuf:"bytes,7,rep,name=accounts_data_slice,json=accountsDataSlice,proto3" json:"accounts_data_slice,omitempty"`
	Ping               *SubscribeRequestPing                          `protobuf:"bytes,9,opt,name=ping,proto3,oneof" json:"ping,omitempty"`
	FromSlot           *uint64                                        `protobuf:"varint,11,opt,name=from_slot,json=fromSlot,proto3,oneof" json:"from_slot,omitempty"`
}

func (x *SubscribeRequest) Reset() {
	*x = SubscribeRequest{}
	mi := &file_geyser_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequest) ProtoMessage() {}

func (x *SubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequest.ProtoReflect.Descriptor instead.
func (*SubscribeRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{0}
}

func (x *SubscribeRequest) GetAccounts() map[string]*SubscribeRequestFilterAccounts {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *SubscribeRequest) GetSlots() map[string]*SubscribeRequestFilterSlots {
	if x != nil {
		return x.Slots
	}
	return nil
}

func (x *SubscribeRequest) GetTransactions() map[string]*SubscribeRequestFilterTransactions {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *SubscribeRequest) GetTransactionsStatus() map[string]*SubscribeRequestFilterTransactions {
	if x != nil {
		return x.TransactionsStatus
	}
	return nil
}

func (x *SubscribeRequest) GetBlocks() map[string]*SubscribeRequestFilterBlocks {
	if x != nil {
		return x.Blocks
	}
	return nil
}

func (x *SubscribeRequest) GetBlocksMeta() map[string]*SubscribeRequestFilterBlocksMeta {
	if x != nil {
		return x.BlocksMeta
	}
	return nil
}

func (x *SubscribeRequest) GetEntry() map[string]*SubscribeRequestFilterEntry {
	if x != nil {
		return x.Entry
	}
	return nil
}

func (x *SubscribeRequest) GetCommitment() CommitmentLevel {
	if x != nil && x.Commitment != nil {
		return *x.Commitment
	}
	return CommitmentLevel_PROCESSED
}

func (x *SubscribeRequest) GetAccountsDataSlice() []*SubscribeRequestAccountsDataSlice {
	if x != nil {
		return x.AccountsDataSlice
	}
	return nil
}

func (x *SubscribeRequest) GetPing() *SubscribeRequestPing {
	if x != nil {
		return x.Ping
	}
	return nil
}

func (x *SubscribeRequest) GetFromSlot() uint64 {
	if x != nil && x.FromSlot != nil {
		return *x.FromSlot
	}
	return 0
}

type SubscribeRequestFilterAccounts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account              []string                                `protobuf:"bytes,2,rep,name=account,proto3" json:"account,omitempty"`
	Owner                []string                                `protobuf:"bytes,3,rep,name=owner,proto3" json:"owner,omitempty"`
	Filters              []*SubscribeRequestFilterAccountsFilter `protobuf:"bytes,4,rep,name=filters,proto3" json:"filters,omitempty"`
	NonemptyTxnSignature *bool                                   `protobuf:"varint,5,opt,name=nonempty_txn_signature,json=nonemptyTxnSignature,proto3,oneof" json:"nonempty_txn_signature,omitempty"`
}

func (x *SubscribeRequestFilterAccounts) Reset() {
	*x = SubscribeRequestFilterAccounts{}
	mi := &file_geyser_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterAccounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterAccounts) ProtoMessage() {}

func (x *SubscribeRequestFilterAccounts) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterAccounts.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterAccounts) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{1}
}

func (x *SubscribeRequestFilterAccounts) GetAccount() []string {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *SubscribeRequestFilterAccounts) GetOwner() []string {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *SubscribeRequestFilterAccounts) GetFilters() []*SubscribeRequestFilterAccountsFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *SubscribeRequestFilterAccounts) GetNonemptyTxnSignature() bool {
	if x != nil && x.NonemptyTxnSignature != nil {
		return *x.NonemptyTxnSignature
	}
	return false
}

type SubscribeRequestFilterAccountsFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Filter:
	//
	//	*SubscribeRequestFilterAccountsFilter_Memcmp
	//	*SubscribeRequestFilterAccountsFilter_Datasize
	//	*SubscribeRequestFilterAccountsFilter_TokenAccountState
	//	*SubscribeRequestFilterAccountsFilter_Lamports
	Filter isSubscribeRequestFilterAccountsFilter_Filter `protobuf_oneof:"filter"`
}

func (x *SubscribeRequestFilterAccountsFilter) Reset() {
	*x = SubscribeRequestFilterAccountsFilter{}
	mi := &file_geyser_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterAccountsFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterAccountsFilter) ProtoMessage() {}

func (x *SubscribeRequestFilterAccountsFilter) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterAccountsFilter.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterAccountsFilter) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{2}
}

func (m *SubscribeRequestFilterAccountsFilter) GetFilter() isSubscribeRequestFilterAccountsFilter_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (x *SubscribeRequestFilterAccountsFilter) GetMemcmp() *SubscribeRequestFilterAccountsFilterMemcmp {
	if x, ok := x.GetFilter().(*SubscribeRequestFilterAccountsFilter_Memcmp); ok {
		return x.Memcmp
	}
	return nil
}

func (x *SubscribeRequestFilterAccountsFilter) GetDatasize() uint64 {
	if x, ok := x.GetFilter().(*SubscribeRequestFilterAccountsFilter_Datasize); ok {
		return x.Datasize
	}
	return 0
}

func (x *SubscribeRequestFilterAccountsFilter) GetTokenAccountState() bool {
	if x, ok := x.GetFilter().(*SubscribeRequestFilterAccountsFilter_TokenAccountState); ok {
		return x.TokenAccountState
	}
	return false
}

func (x *SubscribeRequestFilterAccountsFilter) GetLamports() *SubscribeRequestFilterAccountsFilterLamports {
	if x, ok := x.GetFilter().(*SubscribeRequestFilterAccountsFilter_Lamports); ok {
		return x.Lamports
	}
	return nil
}

type isSubscribeRequestFilterAccountsFilter_Filter interface {
	isSubscribeRequestFilterAccountsFilter_Filter()
}

type SubscribeRequestFilterAccountsFilter_Memcmp struct {
	Memcmp *SubscribeRequestFilterAccountsFilterMemcmp `protobuf:"bytes,1,opt,name=memcmp,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilter_Datasize struct {
	Datasize uint64 `protobuf:"varint,2,opt,name=datasize,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilter_TokenAccountState struct {
	TokenAccountState bool `protobuf:"varint,3,opt,name=token_account_state,json=tokenAccountState,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilter_Lamports struct {
	Lamports *SubscribeRequestFilterAccountsFilterLamports `protobuf:"bytes,4,opt,name=lamports,proto3,oneof"`
}

func (*SubscribeRequestFilterAccountsFilter_Memcmp) isSubscribeRequestFilterAccountsFilter_Filter() {}

func (*SubscribeRequestFilterAccountsFilter_Datasize) isSubscribeRequestFilterAccountsFilter_Filter() {
}

func (*SubscribeRequestFilterAccountsFilter_TokenAccountState) isSubscribeRequestFilterAccountsFilter_Filter() {
}

func (*SubscribeRequestFilterAccountsFilter_Lamports) isSubscribeRequestFilterAccountsFilter_Filter() {
}

type SubscribeRequestFilterAccountsFilterMemcmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint64 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// Types that are assignable to Data:
	//
	//	*SubscribeRequestFilterAccountsFilterMemcmp_Bytes
	//	*SubscribeRequestFilterAccountsFilterMemcmp_Base58
	//	*SubscribeRequestFilterAccountsFilterMemcmp_Base64
	Data isSubscribeRequestFilterAccountsFilterMemcmp_Data `protobuf_oneof:"data"`
}

func (x *SubscribeRequestFilterAccountsFilterMemcmp) Reset() {
	*x = SubscribeRequestFilterAccountsFilterMemcmp{}
	mi := &file_geyser_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterAccountsFilterMemcmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterAccountsFilterMemcmp) ProtoMessage() {}

func (x *SubscribeRequestFilterAccountsFilterMemcmp) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterAccountsFilterMemcmp.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterAccountsFilterMemcmp) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{3}
}

func (x *SubscribeRequestFilterAccountsFilterMemcmp) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (m *SubscribeRequestFilterAccountsFilterMemcmp) GetData() isSubscribeRequestFilterAccountsFilterMemcmp_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *SubscribeRequestFilterAccountsFilterMemcmp) GetBytes() []byte {
	if x, ok := x.GetData().(*SubscribeRequestFilterAccountsFilterMemcmp_Bytes); ok {
		return x.Bytes
	}
	return nil
}

func (x *SubscribeRequestFilterAccountsFilterMemcmp) GetBase58() string {
	if x, ok := x.GetData().(*SubscribeRequestFilterAccountsFilterMemcmp_Base58); ok {
		return x.Base58
	}
	return ""
}

func (x *SubscribeRequestFilterAccountsFilterMemcmp) GetBase64() string {
	if x, ok := x.GetData().(*SubscribeRequestFilterAccountsFilterMemcmp_Base64); ok {
		return x.Base64
	}
	return ""
}

type isSubscribeRequestFilterAccountsFilterMemcmp_Data interface {
	isSubscribeRequestFilterAccountsFilterMemcmp_Data()
}

type SubscribeRequestFilterAccountsFilterMemcmp_Bytes struct {
	Bytes []byte `protobuf:"bytes,2,opt,name=bytes,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilterMemcmp_Base58 struct {
	Base58 string `protobuf:"bytes,3,opt,name=base58,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilterMemcmp_Base64 struct {
	Base64 string `protobuf:"bytes,4,opt,name=base64,proto3,oneof"`
}

func (*SubscribeRequestFilterAccountsFilterMemcmp_Bytes) isSubscribeRequestFilterAccountsFilterMemcmp_Data() {
}

func (*SubscribeRequestFilterAccountsFilterMemcmp_Base58) isSubscribeRequestFilterAccountsFilterMemcmp_Data() {
}

func (*SubscribeRequestFilterAccountsFilterMemcmp_Base64) isSubscribeRequestFilterAccountsFilterMemcmp_Data() {
}

type SubscribeRequestFilterAccountsFilterLamports struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Cmp:
	//
	//	*SubscribeRequestFilterAccountsFilterLamports_Eq
	//	*SubscribeRequestFilterAccountsFilterLamports_Ne
	//	*SubscribeRequestFilterAccountsFilterLamports_Lt
	//	*SubscribeRequestFilterAccountsFilterLamports_Gt
	Cmp isSubscribeRequestFilterAccountsFilterLamports_Cmp `protobuf_oneof:"cmp"`
}

func (x *SubscribeRequestFilterAccountsFilterLamports) Reset() {
	*x = SubscribeRequestFilterAccountsFilterLamports{}
	mi := &file_geyser_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterAccountsFilterLamports) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterAccountsFilterLamports) ProtoMessage() {}

func (x *SubscribeRequestFilterAccountsFilterLamports) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterAccountsFilterLamports.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterAccountsFilterLamports) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{4}
}

func (m *SubscribeRequestFilterAccountsFilterLamports) GetCmp() isSubscribeRequestFilterAccountsFilterLamports_Cmp {
	if m != nil {
		return m.Cmp
	}
	return nil
}

func (x *SubscribeRequestFilterAccountsFilterLamports) GetEq() uint64 {
	if x, ok := x.GetCmp().(*SubscribeRequestFilterAccountsFilterLamports_Eq); ok {
		return x.Eq
	}
	return 0
}

func (x *SubscribeRequestFilterAccountsFilterLamports) GetNe() uint64 {
	if x, ok := x.GetCmp().(*SubscribeRequestFilterAccountsFilterLamports_Ne); ok {
		return x.Ne
	}
	return 0
}

func (x *SubscribeRequestFilterAccountsFilterLamports) GetLt() uint64 {
	if x, ok := x.GetCmp().(*SubscribeRequestFilterAccountsFilterLamports_Lt); ok {
		return x.Lt
	}
	return 0
}

func (x *SubscribeRequestFilterAccountsFilterLamports) GetGt() uint64 {
	if x, ok := x.GetCmp().(*SubscribeRequestFilterAccountsFilterLamports_Gt); ok {
		return x.Gt
	}
	return 0
}

type isSubscribeRequestFilterAccountsFilterLamports_Cmp interface {
	isSubscribeRequestFilterAccountsFilterLamports_Cmp()
}

type SubscribeRequestFilterAccountsFilterLamports_Eq struct {
	Eq uint64 `protobuf:"varint,1,opt,name=eq,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilterLamports_Ne struct {
	Ne uint64 `protobuf:"varint,2,opt,name=ne,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilterLamports_Lt struct {
	Lt uint64 `protobuf:"varint,3,opt,name=lt,proto3,oneof"`
}

type SubscribeRequestFilterAccountsFilterLamports_Gt struct {
	Gt uint64 `protobuf:"varint,4,opt,name=gt,proto3,oneof"`
}

func (*SubscribeRequestFilterAccountsFilterLamports_Eq) isSubscribeRequestFilterAccountsFilterLamports_Cmp() {
}

func (*SubscribeRequestFilterAccountsFilterLamports_Ne) isSubscribeRequestFilterAccountsFilterLamports_Cmp() {
}

func (*SubscribeRequestFilterAccountsFilterLamports_Lt) isSubscribeRequestFilterAccountsFilterLamports_Cmp() {
}

func (*SubscribeRequestFilterAccountsFilterLamports_Gt) isSubscribeRequestFilterAccountsFilterLamports_Cmp() {
}

type SubscribeRequestFilterSlots struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilterByCommitment *bool `protobuf:"varint,1,opt,name=filter_by_commitment,json=filterByCommitment,proto3,oneof" json:"filter_by_commitment,omitempty"`
	InterslotUpdates   *bool `protobuf:"varint,2,opt,name=interslot_updates,json=interslotUpdates,proto3,oneof" json:"interslot_updates,omitempty"`
}

func (x *SubscribeRequestFilterSlots) Reset() {
	*x = SubscribeRequestFilterSlots{}
	mi := &file_geyser_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterSlots) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterSlots) ProtoMessage() {}

func (x *SubscribeRequestFilterSlots) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterSlots.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterSlots) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{5}
}

func (x *SubscribeRequestFilterSlots) GetFilterByCommitment() bool {
	if x != nil && x.FilterByCommitment != nil {
		return *x.FilterByCommitment
	}
	return false
}

func (x *SubscribeRequestFilterSlots) GetInterslotUpdates() bool {
	if x != nil && x.InterslotUpdates != nil {
		return *x.InterslotUpdates
	}
	return false
}

type SubscribeRequestFilterTransactions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vote            *bool    `protobuf:"varint,1,opt,name=vote,proto3,oneof" json:"vote,omitempty"`
	Failed          *bool    `protobuf:"varint,2,opt,name=failed,proto3,oneof" json:"failed,omitempty"`
	Signature       *string  `protobuf:"bytes,5,opt,name=signature,proto3,oneof" json:"signature,omitempty"`
	AccountInclude  []string `protobuf:"bytes,3,rep,name=account_include,json=accountInclude,proto3" json:"account_include,omitempty"`
	AccountExclude  []string `protobuf:"bytes,4,rep,name=account_exclude,json=accountExclude,proto3" json:"account_exclude,omitempty"`
	AccountRequired []string `protobuf:"bytes,6,rep,name=account_required,json=accountRequired,proto3" json:"account_required,omitempty"`
}

func (x *SubscribeRequestFilterTransactions) Reset() {
	*x = SubscribeRequestFilterTransactions{}
	mi := &file_geyser_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterTransactions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterTransactions) ProtoMessage() {}

func (x *SubscribeRequestFilterTransactions) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterTransactions.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterTransactions) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{6}
}

func (x *SubscribeRequestFilterTransactions) GetVote() bool {
	if x != nil && x.Vote != nil {
		return *x.Vote
	}
	return false
}

func (x *SubscribeRequestFilterTransactions) GetFailed() bool {
	if x != nil && x.Failed != nil {
		return *x.Failed
	}
	return false
}

func (x *SubscribeRequestFilterTransactions) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

func (x *SubscribeRequestFilterTransactions) GetAccountInclude() []string {
	if x != nil {
		return x.AccountInclude
	}
	return nil
}

func (x *SubscribeRequestFilterTransactions) GetAccountExclude() []string {
	if x != nil {
		return x.AccountExclude
	}
	return nil
}

func (x *SubscribeRequestFilterTransactions) GetAccountRequired() []string {
	if x != nil {
		return x.AccountRequired
	}
	return nil
}

type SubscribeRequestFilterBlocks struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountInclude      []string `protobuf:"bytes,1,rep,name=account_include,json=accountInclude,proto3" json:"account_include,omitempty"`
	IncludeTransactions *bool    `protobuf:"varint,2,opt,name=include_transactions,json=includeTransactions,proto3,oneof" json:"include_transactions,omitempty"`
	IncludeAccounts     *bool    `protobuf:"varint,3,opt,name=include_accounts,json=includeAccounts,proto3,oneof" json:"include_accounts,omitempty"`
	IncludeEntries      *bool    `protobuf:"varint,4,opt,name=include_entries,json=includeEntries,proto3,oneof" json:"include_entries,omitempty"`
}

func (x *SubscribeRequestFilterBlocks) Reset() {
	*x = SubscribeRequestFilterBlocks{}
	mi := &file_geyser_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterBlocks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterBlocks) ProtoMessage() {}

func (x *SubscribeRequestFilterBlocks) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterBlocks.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterBlocks) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{7}
}

func (x *SubscribeRequestFilterBlocks) GetAccountInclude() []string {
	if x != nil {
		return x.AccountInclude
	}
	return nil
}

func (x *SubscribeRequestFilterBlocks) GetIncludeTransactions() bool {
	if x != nil && x.IncludeTransactions != nil {
		return *x.IncludeTransactions
	}
	return false
}

func (x *SubscribeRequestFilterBlocks) GetIncludeAccounts() bool {
	if x != nil && x.IncludeAccounts != nil {
		return *x.IncludeAccounts
	}
	return false
}

func (x *SubscribeRequestFilterBlocks) GetIncludeEntries() bool {
	if x != nil && x.IncludeEntries != nil {
		return *x.IncludeEntries
	}
	return false
}

type SubscribeRequestFilterBlocksMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SubscribeRequestFilterBlocksMeta) Reset() {
	*x = SubscribeRequestFilterBlocksMeta{}
	mi := &file_geyser_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterBlocksMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterBlocksMeta) ProtoMessage() {}

func (x *SubscribeRequestFilterBlocksMeta) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterBlocksMeta.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterBlocksMeta) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{8}
}

type SubscribeRequestFilterEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SubscribeRequestFilterEntry) Reset() {
	*x = SubscribeRequestFilterEntry{}
	mi := &file_geyser_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestFilterEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestFilterEntry) ProtoMessage() {}

func (x *SubscribeRequestFilterEntry) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestFilterEntry.ProtoReflect.Descriptor instead.
func (*SubscribeRequestFilterEntry) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{9}
}

type SubscribeRequestAccountsDataSlice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint64 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Length uint64 `protobuf:"varint,2,opt,name=length,proto3" json:"length,omitempty"`
}

func (x *SubscribeRequestAccountsDataSlice) Reset() {
	*x = SubscribeRequestAccountsDataSlice{}
	mi := &file_geyser_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestAccountsDataSlice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestAccountsDataSlice) ProtoMessage() {}

func (x *SubscribeRequestAccountsDataSlice) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestAccountsDataSlice.ProtoReflect.Descriptor instead.
func (*SubscribeRequestAccountsDataSlice) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{10}
}

func (x *SubscribeRequestAccountsDataSlice) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *SubscribeRequestAccountsDataSlice) GetLength() uint64 {
	if x != nil {
		return x.Length
	}
	return 0
}

type SubscribeRequestPing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SubscribeRequestPing) Reset() {
	*x = SubscribeRequestPing{}
	mi := &file_geyser_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequestPing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequestPing) ProtoMessage() {}

func (x *SubscribeRequestPing) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequestPing.ProtoReflect.Descriptor instead.
func (*SubscribeRequestPing) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{11}
}

func (x *SubscribeRequestPing) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SubscribeUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters []string `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
	// Types that are assignable to UpdateOneof:
	//
	//	*SubscribeUpdate_Account
	//	*SubscribeUpdate_Slot
	//	*SubscribeUpdate_Transaction
	//	*SubscribeUpdate_TransactionStatus
	//	*SubscribeUpdate_Block
	//	*SubscribeUpdate_Ping
	//	*SubscribeUpdate_Pong
	//	*SubscribeUpdate_BlockMeta
	//	*SubscribeUpdate_Entry
	UpdateOneof isSubscribeUpdate_UpdateOneof `protobuf_oneof:"update_oneof"`
	CreatedAt   *timestamppb.Timestamp        `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *SubscribeUpdate) Reset() {
	*x = SubscribeUpdate{}
	mi := &file_geyser_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdate) ProtoMessage() {}

func (x *SubscribeUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdate.ProtoReflect.Descriptor instead.
func (*SubscribeUpdate) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{12}
}

func (x *SubscribeUpdate) GetFilters() []string {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (m *SubscribeUpdate) GetUpdateOneof() isSubscribeUpdate_UpdateOneof {
	if m != nil {
		return m.UpdateOneof
	}
	return nil
}

func (x *SubscribeUpdate) GetAccount() *SubscribeUpdateAccount {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_Account); ok {
		return x.Account
	}
	return nil
}

func (x *SubscribeUpdate) GetSlot() *SubscribeUpdateSlot {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_Slot); ok {
		return x.Slot
	}
	return nil
}

func (x *SubscribeUpdate) GetTransaction() *SubscribeUpdateTransaction {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_Transaction); ok {
		return x.Transaction
	}
	return nil
}

func (x *SubscribeUpdate) GetTransactionStatus() *SubscribeUpdateTransactionStatus {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_TransactionStatus); ok {
		return x.TransactionStatus
	}
	return nil
}

func (x *SubscribeUpdate) GetBlock() *SubscribeUpdateBlock {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_Block); ok {
		return x.Block
	}
	return nil
}

func (x *SubscribeUpdate) GetPing() *SubscribeUpdatePing {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *SubscribeUpdate) GetPong() *SubscribeUpdatePong {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *SubscribeUpdate) GetBlockMeta() *SubscribeUpdateBlockMeta {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_BlockMeta); ok {
		return x.BlockMeta
	}
	return nil
}

func (x *SubscribeUpdate) GetEntry() *SubscribeUpdateEntry {
	if x, ok := x.GetUpdateOneof().(*SubscribeUpdate_Entry); ok {
		return x.Entry
	}
	return nil
}

func (x *SubscribeUpdate) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type isSubscribeUpdate_UpdateOneof interface {
	isSubscribeUpdate_UpdateOneof()
}

type SubscribeUpdate_Account struct {
	Account *SubscribeUpdateAccount `protobuf:"bytes,2,opt,name=account,proto3,oneof"`
}

type SubscribeUpdate_Slot struct {
	Slot *SubscribeUpdateSlot `protobuf:"bytes,3,opt,name=slot,proto3,oneof"`
}

type SubscribeUpdate_Transaction struct {
	Transaction *SubscribeUpdateTransaction `protobuf:"bytes,4,opt,name=transaction,proto3,oneof"`
}

type SubscribeUpdate_TransactionStatus struct {
	TransactionStatus *SubscribeUpdateTransactionStatus `protobuf:"bytes,10,opt,name=transaction_status,json=transactionStatus,proto3,oneof"`
}

type SubscribeUpdate_Block struct {
	Block *SubscribeUpdateBlock `protobuf:"bytes,5,opt,name=block,proto3,oneof"`
}

type SubscribeUpdate_Ping struct {
	Ping *SubscribeUpdatePing `protobuf:"bytes,6,opt,name=ping,proto3,oneof"`
}

type SubscribeUpdate_Pong struct {
	Pong *SubscribeUpdatePong `protobuf:"bytes,9,opt,name=pong,proto3,oneof"`
}

type SubscribeUpdate_BlockMeta struct {
	BlockMeta *SubscribeUpdateBlockMeta `protobuf:"bytes,7,opt,name=block_meta,json=blockMeta,proto3,oneof"`
}

type SubscribeUpdate_Entry struct {
	Entry *SubscribeUpdateEntry `protobuf:"bytes,8,opt,name=entry,proto3,oneof"`
}

func (*SubscribeUpdate_Account) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_Slot) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_Transaction) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_TransactionStatus) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_Block) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_Ping) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_Pong) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_BlockMeta) isSubscribeUpdate_UpdateOneof() {}

func (*SubscribeUpdate_Entry) isSubscribeUpdate_UpdateOneof() {}

type SubscribeUpdateAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account   *SubscribeUpdateAccountInfo `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Slot      uint64                      `protobuf:"varint,2,opt,name=slot,proto3" json:"slot,omitempty"`
	IsStartup bool                        `protobuf:"varint,3,opt,name=is_startup,json=isStartup,proto3" json:"is_startup,omitempty"`
}

func (x *SubscribeUpdateAccount) Reset() {
	*x = SubscribeUpdateAccount{}
	mi := &file_geyser_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateAccount) ProtoMessage() {}

func (x *SubscribeUpdateAccount) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateAccount.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateAccount) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{13}
}

func (x *SubscribeUpdateAccount) GetAccount() *SubscribeUpdateAccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *SubscribeUpdateAccount) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SubscribeUpdateAccount) GetIsStartup() bool {
	if x != nil {
		return x.IsStartup
	}
	return false
}

type SubscribeUpdateAccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pubkey       []byte `protobuf:"bytes,1,opt,name=pubkey,proto3" json:"pubkey,omitempty"`
	Lamports     uint64 `protobuf:"varint,2,opt,name=lamports,proto3" json:"lamports,omitempty"`
	Owner        []byte `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner,omitempty"`
	Executable   bool   `protobuf:"varint,4,opt,name=executable,proto3" json:"executable,omitempty"`
	RentEpoch    uint64 `protobuf:"varint,5,opt,name=rent_epoch,json=rentEpoch,proto3" json:"rent_epoch,omitempty"`
	Data         []byte `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
	WriteVersion uint64 `protobuf:"varint,7,opt,name=write_version,json=writeVersion,proto3" json:"write_version,omitempty"`
	TxnSignature []byte `protobuf:"bytes,8,opt,name=txn_signature,json=txnSignature,proto3,oneof" json:"txn_signature,omitempty"`
}

func (x *SubscribeUpdateAccountInfo) Reset() {
	*x = SubscribeUpdateAccountInfo{}
	mi := &file_geyser_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateAccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateAccountInfo) ProtoMessage() {}

func (x *SubscribeUpdateAccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateAccountInfo.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateAccountInfo) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{14}
}

func (x *SubscribeUpdateAccountInfo) GetPubkey() []byte {
	if x != nil {
		return x.Pubkey
	}
	return nil
}

func (x *SubscribeUpdateAccountInfo) GetLamports() uint64 {
	if x != nil {
		return x.Lamports
	}
	return 0
}

func (x *SubscribeUpdateAccountInfo) GetOwner() []byte {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *SubscribeUpdateAccountInfo) GetExecutable() bool {
	if x != nil {
		return x.Executable
	}
	return false
}

func (x *SubscribeUpdateAccountInfo) GetRentEpoch() uint64 {
	if x != nil {
		return x.RentEpoch
	}
	return 0
}

func (x *SubscribeUpdateAccountInfo) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SubscribeUpdateAccountInfo) GetWriteVersion() uint64 {
	if x != nil {
		return x.WriteVersion
	}
	return 0
}

func (x *SubscribeUpdateAccountInfo) GetTxnSignature() []byte {
	if x != nil {
		return x.TxnSignature
	}
	return nil
}

type SubscribeUpdateSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot      uint64     `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Parent    *uint64    `protobuf:"varint,2,opt,name=parent,proto3,oneof" json:"parent,omitempty"`
	Status    SlotStatus `protobuf:"varint,3,opt,name=status,proto3,enum=geyser.SlotStatus" json:"status,omitempty"`
	DeadError *string    `protobuf:"bytes,4,opt,name=dead_error,json=deadError,proto3,oneof" json:"dead_error,omitempty"`
}

func (x *SubscribeUpdateSlot) Reset() {
	*x = SubscribeUpdateSlot{}
	mi := &file_geyser_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateSlot) ProtoMessage() {}

func (x *SubscribeUpdateSlot) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateSlot.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateSlot) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{15}
}

func (x *SubscribeUpdateSlot) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SubscribeUpdateSlot) GetParent() uint64 {
	if x != nil && x.Parent != nil {
		return *x.Parent
	}
	return 0
}

func (x *SubscribeUpdateSlot) GetStatus() SlotStatus {
	if x != nil {
		return x.Status
	}
	return SlotStatus_SLOT_PROCESSED
}

func (x *SubscribeUpdateSlot) GetDeadError() string {
	if x != nil && x.DeadError != nil {
		return *x.DeadError
	}
	return ""
}

type SubscribeUpdateTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transaction *SubscribeUpdateTransactionInfo `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"`
	Slot        uint64                          `protobuf:"varint,2,opt,name=slot,proto3" json:"slot,omitempty"`
}

func (x *SubscribeUpdateTransaction) Reset() {
	*x = SubscribeUpdateTransaction{}
	mi := &file_geyser_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateTransaction) ProtoMessage() {}

func (x *SubscribeUpdateTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateTransaction.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateTransaction) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{16}
}

func (x *SubscribeUpdateTransaction) GetTransaction() *SubscribeUpdateTransactionInfo {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *SubscribeUpdateTransaction) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

type SubscribeUpdateTransactionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Signature   []byte                 `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`
	IsVote      bool                   `protobuf:"varint,2,opt,name=is_vote,json=isVote,proto3" json:"is_vote,omitempty"`
	Transaction *Transaction           `protobuf:"bytes,3,opt,name=transaction,proto3" json:"transaction,omitempty"`
	Meta        *TransactionStatusMeta `protobuf:"bytes,4,opt,name=meta,proto3" json:"meta,omitempty"`
	Index       uint64                 `protobuf:"varint,5,opt,name=index,proto3" json:"index,omitempty"`
}

func (x *SubscribeUpdateTransactionInfo) Reset() {
	*x = SubscribeUpdateTransactionInfo{}
	mi := &file_geyser_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateTransactionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateTransactionInfo) ProtoMessage() {}

func (x *SubscribeUpdateTransactionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateTransactionInfo.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateTransactionInfo) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{17}
}

func (x *SubscribeUpdateTransactionInfo) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *SubscribeUpdateTransactionInfo) GetIsVote() bool {
	if x != nil {
		return x.IsVote
	}
	return false
}

func (x *SubscribeUpdateTransactionInfo) GetTransaction() *Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *SubscribeUpdateTransactionInfo) GetMeta() *TransactionStatusMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *SubscribeUpdateTransactionInfo) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

type SubscribeUpdateTransactionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot      uint64            `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Signature []byte            `protobuf:"bytes,2,opt,name=signature,proto3" json:"signature,omitempty"`
	IsVote    bool              `protobuf:"varint,3,opt,name=is_vote,json=isVote,proto3" json:"is_vote,omitempty"`
	Index     uint64            `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	Err       *TransactionError `protobuf:"bytes,5,opt,name=err,proto3" json:"err,omitempty"`
}

func (x *SubscribeUpdateTransactionStatus) Reset() {
	*x = SubscribeUpdateTransactionStatus{}
	mi := &file_geyser_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateTransactionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateTransactionStatus) ProtoMessage() {}

func (x *SubscribeUpdateTransactionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateTransactionStatus.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateTransactionStatus) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{18}
}

func (x *SubscribeUpdateTransactionStatus) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SubscribeUpdateTransactionStatus) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *SubscribeUpdateTransactionStatus) GetIsVote() bool {
	if x != nil {
		return x.IsVote
	}
	return false
}

func (x *SubscribeUpdateTransactionStatus) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SubscribeUpdateTransactionStatus) GetErr() *TransactionError {
	if x != nil {
		return x.Err
	}
	return nil
}

type SubscribeUpdateBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot                     uint64                            `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Blockhash                string                            `protobuf:"bytes,2,opt,name=blockhash,proto3" json:"blockhash,omitempty"`
	Rewards                  *Rewards                          `protobuf:"bytes,3,opt,name=rewards,proto3" json:"rewards,omitempty"`
	BlockTime                *UnixTimestamp                    `protobuf:"bytes,4,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	BlockHeight              *BlockHeight                      `protobuf:"bytes,5,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	ParentSlot               uint64                            `protobuf:"varint,7,opt,name=parent_slot,json=parentSlot,proto3" json:"parent_slot,omitempty"`
	ParentBlockhash          string                            `protobuf:"bytes,8,opt,name=parent_blockhash,json=parentBlockhash,proto3" json:"parent_blockhash,omitempty"`
	ExecutedTransactionCount uint64                            `protobuf:"varint,9,opt,name=executed_transaction_count,json=executedTransactionCount,proto3" json:"executed_transaction_count,omitempty"`
	Transactions             []*SubscribeUpdateTransactionInfo `protobuf:"bytes,6,rep,name=transactions,proto3" json:"transactions,omitempty"`
	UpdatedAccountCount      uint64                            `protobuf:"varint,10,opt,name=updated_account_count,json=updatedAccountCount,proto3" json:"updated_account_count,omitempty"`
	Accounts                 []*SubscribeUpdateAccountInfo     `protobuf:"bytes,11,rep,name=accounts,proto3" json:"accounts,omitempty"`
	EntriesCount             uint64                            `protobuf:"varint,12,opt,name=entries_count,json=entriesCount,proto3" json:"entries_count,omitempty"`
	Entries                  []*SubscribeUpdateEntry           `protobuf:"bytes,13,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (x *SubscribeUpdateBlock) Reset() {
	*x = SubscribeUpdateBlock{}
	mi := &file_geyser_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateBlock) ProtoMessage() {}

func (x *SubscribeUpdateBlock) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateBlock.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateBlock) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{19}
}

func (x *SubscribeUpdateBlock) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SubscribeUpdateBlock) GetBlockhash() string {
	if x != nil {
		return x.Blockhash
	}
	return ""
}

func (x *SubscribeUpdateBlock) GetRewards() *Rewards {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *SubscribeUpdateBlock) GetBlockTime() *UnixTimestamp {
	if x != nil {
		return x.BlockTime
	}
	return nil
}

func (x *SubscribeUpdateBlock) GetBlockHeight() *BlockHeight {
	if x != nil {
		return x.BlockHeight
	}
	return nil
}

func (x *SubscribeUpdateBlock) GetParentSlot() uint64 {
	if x != nil {
		return x.ParentSlot
	}
	return 0
}

func (x *SubscribeUpdateBlock) GetParentBlockhash() string {
	if x != nil {
		return x.ParentBlockhash
	}
	return ""
}

func (x *SubscribeUpdateBlock) GetExecutedTransactionCount() uint64 {
	if x != nil {
		return x.ExecutedTransactionCount
	}
	return 0
}

func (x *SubscribeUpdateBlock) GetTransactions() []*SubscribeUpdateTransactionInfo {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *SubscribeUpdateBlock) GetUpdatedAccountCount() uint64 {
	if x != nil {
		return x.UpdatedAccountCount
	}
	return 0
}

func (x *SubscribeUpdateBlock) GetAccounts() []*SubscribeUpdateAccountInfo {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *SubscribeUpdateBlock) GetEntriesCount() uint64 {
	if x != nil {
		return x.EntriesCount
	}
	return 0
}

func (x *SubscribeUpdateBlock) GetEntries() []*SubscribeUpdateEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

type SubscribeUpdateBlockMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot                     uint64         `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Blockhash                string         `protobuf:"bytes,2,opt,name=blockhash,proto3" json:"blockhash,omitempty"`
	Rewards                  *Rewards       `protobuf:"bytes,3,opt,name=rewards,proto3" json:"rewards,omitempty"`
	BlockTime                *UnixTimestamp `protobuf:"bytes,4,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	BlockHeight              *BlockHeight   `protobuf:"bytes,5,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	ParentSlot               uint64         `protobuf:"varint,6,opt,name=parent_slot,json=parentSlot,proto3" json:"parent_slot,omitempty"`
	ParentBlockhash          string         `protobuf:"bytes,7,opt,name=parent_blockhash,json=parentBlockhash,proto3" json:"parent_blockhash,omitempty"`
	ExecutedTransactionCount uint64         `protobuf:"varint,8,opt,name=executed_transaction_count,json=executedTransactionCount,proto3" json:"executed_transaction_count,omitempty"`
	EntriesCount             uint64         `protobuf:"varint,9,opt,name=entries_count,json=entriesCount,proto3" json:"entries_count,omitempty"`
}

func (x *SubscribeUpdateBlockMeta) Reset() {
	*x = SubscribeUpdateBlockMeta{}
	mi := &file_geyser_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateBlockMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateBlockMeta) ProtoMessage() {}

func (x *SubscribeUpdateBlockMeta) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateBlockMeta.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateBlockMeta) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{20}
}

func (x *SubscribeUpdateBlockMeta) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SubscribeUpdateBlockMeta) GetBlockhash() string {
	if x != nil {
		return x.Blockhash
	}
	return ""
}

func (x *SubscribeUpdateBlockMeta) GetRewards() *Rewards {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *SubscribeUpdateBlockMeta) GetBlockTime() *UnixTimestamp {
	if x != nil {
		return x.BlockTime
	}
	return nil
}

func (x *SubscribeUpdateBlockMeta) GetBlockHeight() *BlockHeight {
	if x != nil {
		return x.BlockHeight
	}
	return nil
}

func (x *SubscribeUpdateBlockMeta) GetParentSlot() uint64 {
	if x != nil {
		return x.ParentSlot
	}
	return 0
}

func (x *SubscribeUpdateBlockMeta) GetParentBlockhash() string {
	if x != nil {
		return x.ParentBlockhash
	}
	return ""
}

func (x *SubscribeUpdateBlockMeta) GetExecutedTransactionCount() uint64 {
	if x != nil {
		return x.ExecutedTransactionCount
	}
	return 0
}

func (x *SubscribeUpdateBlockMeta) GetEntriesCount() uint64 {
	if x != nil {
		return x.EntriesCount
	}
	return 0
}

type SubscribeUpdateEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot                     uint64 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Index                    uint64 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	NumHashes                uint64 `protobuf:"varint,3,opt,name=num_hashes,json=numHashes,proto3" json:"num_hashes,omitempty"`
	Hash                     []byte `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	ExecutedTransactionCount uint64 `protobuf:"varint,5,opt,name=executed_transaction_count,json=executedTransactionCount,proto3" json:"executed_transaction_count,omitempty"`
	StartingTransactionIndex uint64 `protobuf:"varint,6,opt,name=starting_transaction_index,json=startingTransactionIndex,proto3" json:"starting_transaction_index,omitempty"` // added in v1.18, for solana 1.17 value is always 0
}

func (x *SubscribeUpdateEntry) Reset() {
	*x = SubscribeUpdateEntry{}
	mi := &file_geyser_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdateEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdateEntry) ProtoMessage() {}

func (x *SubscribeUpdateEntry) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdateEntry.ProtoReflect.Descriptor instead.
func (*SubscribeUpdateEntry) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{21}
}

func (x *SubscribeUpdateEntry) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SubscribeUpdateEntry) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SubscribeUpdateEntry) GetNumHashes() uint64 {
	if x != nil {
		return x.NumHashes
	}
	return 0
}

func (x *SubscribeUpdateEntry) GetHash() []byte {
	if x != nil {
		return x.Hash
	}
	return nil
}

func (x *SubscribeUpdateEntry) GetExecutedTransactionCount() uint64 {
	if x != nil {
		return x.ExecutedTransactionCount
	}
	return 0
}

func (x *SubscribeUpdateEntry) GetStartingTransactionIndex() uint64 {
	if x != nil {
		return x.StartingTransactionIndex
	}
	return 0
}

type SubscribeUpdatePing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SubscribeUpdatePing) Reset() {
	*x = SubscribeUpdatePing{}
	mi := &file_geyser_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdatePing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdatePing) ProtoMessage() {}

func (x *SubscribeUpdatePing) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdatePing.ProtoReflect.Descriptor instead.
func (*SubscribeUpdatePing) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{22}
}

type SubscribeUpdatePong struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SubscribeUpdatePong) Reset() {
	*x = SubscribeUpdatePong{}
	mi := &file_geyser_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeUpdatePong) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeUpdatePong) ProtoMessage() {}

func (x *SubscribeUpdatePong) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeUpdatePong.ProtoReflect.Descriptor instead.
func (*SubscribeUpdatePong) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{23}
}

func (x *SubscribeUpdatePong) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SubscribeReplayInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SubscribeReplayInfoRequest) Reset() {
	*x = SubscribeReplayInfoRequest{}
	mi := &file_geyser_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeReplayInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeReplayInfoRequest) ProtoMessage() {}

func (x *SubscribeReplayInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeReplayInfoRequest.ProtoReflect.Descriptor instead.
func (*SubscribeReplayInfoRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{24}
}

type SubscribeReplayInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstAvailable *uint64 `protobuf:"varint,1,opt,name=first_available,json=firstAvailable,proto3,oneof" json:"first_available,omitempty"`
}

func (x *SubscribeReplayInfoResponse) Reset() {
	*x = SubscribeReplayInfoResponse{}
	mi := &file_geyser_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeReplayInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeReplayInfoResponse) ProtoMessage() {}

func (x *SubscribeReplayInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeReplayInfoResponse.ProtoReflect.Descriptor instead.
func (*SubscribeReplayInfoResponse) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{25}
}

func (x *SubscribeReplayInfoResponse) GetFirstAvailable() uint64 {
	if x != nil && x.FirstAvailable != nil {
		return *x.FirstAvailable
	}
	return 0
}

type PingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	mi := &file_geyser_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{26}
}

func (x *PingRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type PongResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *PongResponse) Reset() {
	*x = PongResponse{}
	mi := &file_geyser_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PongResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PongResponse) ProtoMessage() {}

func (x *PongResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PongResponse.ProtoReflect.Descriptor instead.
func (*PongResponse) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{27}
}

func (x *PongResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetLatestBlockhashRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commitment *CommitmentLevel `protobuf:"varint,1,opt,name=commitment,proto3,enum=geyser.CommitmentLevel,oneof" json:"commitment,omitempty"`
}

func (x *GetLatestBlockhashRequest) Reset() {
	*x = GetLatestBlockhashRequest{}
	mi := &file_geyser_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLatestBlockhashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestBlockhashRequest) ProtoMessage() {}

func (x *GetLatestBlockhashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestBlockhashRequest.ProtoReflect.Descriptor instead.
func (*GetLatestBlockhashRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{28}
}

func (x *GetLatestBlockhashRequest) GetCommitment() CommitmentLevel {
	if x != nil && x.Commitment != nil {
		return *x.Commitment
	}
	return CommitmentLevel_PROCESSED
}

type GetLatestBlockhashResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot                 uint64 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Blockhash            string `protobuf:"bytes,2,opt,name=blockhash,proto3" json:"blockhash,omitempty"`
	LastValidBlockHeight uint64 `protobuf:"varint,3,opt,name=last_valid_block_height,json=lastValidBlockHeight,proto3" json:"last_valid_block_height,omitempty"`
}

func (x *GetLatestBlockhashResponse) Reset() {
	*x = GetLatestBlockhashResponse{}
	mi := &file_geyser_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLatestBlockhashResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestBlockhashResponse) ProtoMessage() {}

func (x *GetLatestBlockhashResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestBlockhashResponse.ProtoReflect.Descriptor instead.
func (*GetLatestBlockhashResponse) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{29}
}

func (x *GetLatestBlockhashResponse) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *GetLatestBlockhashResponse) GetBlockhash() string {
	if x != nil {
		return x.Blockhash
	}
	return ""
}

func (x *GetLatestBlockhashResponse) GetLastValidBlockHeight() uint64 {
	if x != nil {
		return x.LastValidBlockHeight
	}
	return 0
}

type GetBlockHeightRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commitment *CommitmentLevel `protobuf:"varint,1,opt,name=commitment,proto3,enum=geyser.CommitmentLevel,oneof" json:"commitment,omitempty"`
}

func (x *GetBlockHeightRequest) Reset() {
	*x = GetBlockHeightRequest{}
	mi := &file_geyser_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockHeightRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockHeightRequest) ProtoMessage() {}

func (x *GetBlockHeightRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockHeightRequest.ProtoReflect.Descriptor instead.
func (*GetBlockHeightRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{30}
}

func (x *GetBlockHeightRequest) GetCommitment() CommitmentLevel {
	if x != nil && x.Commitment != nil {
		return *x.Commitment
	}
	return CommitmentLevel_PROCESSED
}

type GetBlockHeightResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockHeight uint64 `protobuf:"varint,1,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
}

func (x *GetBlockHeightResponse) Reset() {
	*x = GetBlockHeightResponse{}
	mi := &file_geyser_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockHeightResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockHeightResponse) ProtoMessage() {}

func (x *GetBlockHeightResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockHeightResponse.ProtoReflect.Descriptor instead.
func (*GetBlockHeightResponse) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{31}
}

func (x *GetBlockHeightResponse) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

type GetSlotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commitment *CommitmentLevel `protobuf:"varint,1,opt,name=commitment,proto3,enum=geyser.CommitmentLevel,oneof" json:"commitment,omitempty"`
}

func (x *GetSlotRequest) Reset() {
	*x = GetSlotRequest{}
	mi := &file_geyser_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSlotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSlotRequest) ProtoMessage() {}

func (x *GetSlotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSlotRequest.ProtoReflect.Descriptor instead.
func (*GetSlotRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{32}
}

func (x *GetSlotRequest) GetCommitment() CommitmentLevel {
	if x != nil && x.Commitment != nil {
		return *x.Commitment
	}
	return CommitmentLevel_PROCESSED
}

type GetSlotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot uint64 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
}

func (x *GetSlotResponse) Reset() {
	*x = GetSlotResponse{}
	mi := &file_geyser_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSlotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSlotResponse) ProtoMessage() {}

func (x *GetSlotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSlotResponse.ProtoReflect.Descriptor instead.
func (*GetSlotResponse) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{33}
}

func (x *GetSlotResponse) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

type GetVersionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetVersionRequest) Reset() {
	*x = GetVersionRequest{}
	mi := &file_geyser_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVersionRequest) ProtoMessage() {}

func (x *GetVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVersionRequest.ProtoReflect.Descriptor instead.
func (*GetVersionRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{34}
}

type GetVersionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *GetVersionResponse) Reset() {
	*x = GetVersionResponse{}
	mi := &file_geyser_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVersionResponse) ProtoMessage() {}

func (x *GetVersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVersionResponse.ProtoReflect.Descriptor instead.
func (*GetVersionResponse) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{35}
}

func (x *GetVersionResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type IsBlockhashValidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Blockhash  string           `protobuf:"bytes,1,opt,name=blockhash,proto3" json:"blockhash,omitempty"`
	Commitment *CommitmentLevel `protobuf:"varint,2,opt,name=commitment,proto3,enum=geyser.CommitmentLevel,oneof" json:"commitment,omitempty"`
}

func (x *IsBlockhashValidRequest) Reset() {
	*x = IsBlockhashValidRequest{}
	mi := &file_geyser_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsBlockhashValidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsBlockhashValidRequest) ProtoMessage() {}

func (x *IsBlockhashValidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsBlockhashValidRequest.ProtoReflect.Descriptor instead.
func (*IsBlockhashValidRequest) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{36}
}

func (x *IsBlockhashValidRequest) GetBlockhash() string {
	if x != nil {
		return x.Blockhash
	}
	return ""
}

func (x *IsBlockhashValidRequest) GetCommitment() CommitmentLevel {
	if x != nil && x.Commitment != nil {
		return *x.Commitment
	}
	return CommitmentLevel_PROCESSED
}

type IsBlockhashValidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot  uint64 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Valid bool   `protobuf:"varint,2,opt,name=valid,proto3" json:"valid,omitempty"`
}

func (x *IsBlockhashValidResponse) Reset() {
	*x = IsBlockhashValidResponse{}
	mi := &file_geyser_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsBlockhashValidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsBlockhashValidResponse) ProtoMessage() {}

func (x *IsBlockhashValidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geyser_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsBlockhashValidResponse.ProtoReflect.Descriptor instead.
func (*IsBlockhashValidResponse) Descriptor() ([]byte, []int) {
	return file_geyser_proto_rawDescGZIP(), []int{37}
}

func (x *IsBlockhashValidResponse) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *IsBlockhashValidResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

var File_geyser_proto protoreflect.FileDescriptor

var file_geyser_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2d,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x0b,
	0x0a, 0x10, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x42, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x05, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x6c, 0x6f, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x73, 0x6c, 0x6f, 0x74,
	0x73, 0x12, 0x4e, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x61, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x73, 0x12, 0x49, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x39, 0x0a,
	0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67,
	0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x3c, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x67,
	0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6c, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x52, 0x11,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6c, 0x69, 0x63,
	0x65, 0x12, 0x35, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x01, 0x52,
	0x04, 0x70, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x48, 0x02, 0x52, 0x08, 0x66,
	0x72, 0x6f, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x88, 0x01, 0x01, 0x1a, 0x63, 0x0a, 0x0d, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67,
	0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x5d, 0x0a, 0x0a, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x6c,
	0x6f, 0x74, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x6b,
	0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x71, 0x0a, 0x17, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5f,
	0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x67, 0x0a, 0x0f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5d, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x70, 0x69, 0x6e, 0x67, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x22, 0xee, 0x01,
	0x0a, 0x1e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x12, 0x46, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x16, 0x6e, 0x6f, 0x6e, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x14, 0x6e, 0x6f, 0x6e, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x54, 0x78, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x6e, 0x6f, 0x6e, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0xa2,
	0x02, 0x0a, 0x24, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x63, 0x6d,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x4d, 0x65, 0x6d, 0x63, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x06, 0x6d,
	0x65, 0x6d, 0x63, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x11, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x52, 0x0a, 0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x48, 0x00, 0x52,
	0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x42, 0x08, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x22, 0x98, 0x01, 0x0a, 0x2a, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4d, 0x65, 0x6d, 0x63,
	0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x05, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x05, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x18, 0x0a, 0x06, 0x62, 0x61, 0x73, 0x65, 0x35, 0x38, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x62, 0x61, 0x73, 0x65, 0x35, 0x38, 0x12, 0x18, 0x0a, 0x06,
	0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06,
	0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x7d,
	0x0a, 0x2c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x10,
	0x0a, 0x02, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x02, 0x65, 0x71,
	0x12, 0x10, 0x0a, 0x02, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x02,
	0x6e, 0x65, 0x12, 0x10, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00,
	0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x48, 0x00, 0x52, 0x02, 0x67, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x63, 0x6d, 0x70, 0x22, 0xb5, 0x01,
	0x0a, 0x1b, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x35, 0x0a,
	0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x12, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x6c, 0x6f,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x01, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x6c, 0x6f, 0x74, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x22, 0x9c, 0x02, 0x0a, 0x22, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x17, 0x0a, 0x04,
	0x76, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x76, 0x6f,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x76, 0x6f, 0x74, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x22, 0x9f, 0x02, 0x0a, 0x1c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x36,
	0x0a, 0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x01, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x02, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x88, 0x01, 0x01, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22, 0x22, 0x0a, 0x20, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x1d, 0x0a, 0x1b, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x22, 0x53, 0x0a, 0x21, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x22, 0x26,
	0x0a, 0x14, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x9d, 0x05, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x31, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x48, 0x00, 0x52, 0x04, 0x73,
	0x6c, 0x6f, 0x74, 0x12, 0x46, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0b,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x12, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x48, 0x00, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x05, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x05, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x31, 0x0a, 0x04,
	0x70, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x65, 0x79,
	0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12,
	0x31, 0x0a, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6f,
	0x6e, 0x67, 0x12, 0x41, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x89, 0x01, 0x0a, 0x16, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x3c, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73,
	0x6c, 0x6f, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x75, 0x70, 0x22, 0x9a, 0x02, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6c, 0x61, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x23,
	0x0a, 0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0d, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x78,
	0x6e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22,
	0xb0, 0x01, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x1b, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x64, 0x65, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x64, 0x65, 0x61, 0x64,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x22, 0x7a, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x48, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c,
	0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x22, 0x85,
	0x02, 0x0a, 0x1e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x76, 0x6f, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x69, 0x73, 0x56, 0x6f, 0x74, 0x65, 0x12, 0x4c, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xc6, 0x01, 0x0a, 0x20, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x76, 0x6f, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x69, 0x73, 0x56, 0x6f, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x41, 0x0a, 0x03,
	0x65, 0x72, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x6f, 0x6c, 0x61,
	0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x03, 0x65, 0x72, 0x72, 0x22,
	0xcd, 0x05, 0x0a, 0x14, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x12, 0x40, 0x0a, 0x07, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x73, 0x6f,
	0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x4b, 0x0a, 0x0a,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x2e, 0x55, 0x6e, 0x69, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x0b, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x68, 0x61, 0x73, 0x68, 0x12, 0x3c, 0x0a, 0x1a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x18, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32,
	0x0a, 0x15, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22,
	0xd9, 0x03, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x12, 0x40,
	0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x4b, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x55, 0x6e, 0x69, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4d, 0x0a,
	0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52,
	0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x29, 0x0a,
	0x10, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x12, 0x3c, 0x0a, 0x1a, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x18, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xef, 0x01, 0x0a, 0x14,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1d,
	0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x12, 0x3c, 0x0a, 0x1a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x18, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3c, 0x0a, 0x1a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x18, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x15, 0x0a,
	0x13, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x69, 0x6e, 0x67, 0x22, 0x25, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1c, 0x0a, 0x1a, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5f, 0x0a, 0x1b, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x23, 0x0a, 0x0b, 0x50, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x24, 0x0a, 0x0c, 0x50, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x68, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48,
	0x00, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x85, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c,
	0x6f, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68,
	0x12, 0x35, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x64, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3c, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48, 0x00, 0x52,
	0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x3b, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x5d, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x25, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x53, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74,
	0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x84, 0x01, 0x0a, 0x17, 0x49, 0x73, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x68, 0x61, 0x73, 0x68, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x3c, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x0a,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x44, 0x0a, 0x18,
	0x49, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x2a, 0x3e, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44,
	0x10, 0x02, 0x2a, 0xa1, 0x01, 0x0a, 0x0a, 0x53, 0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4c, 0x4f,
	0x54, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a,
	0x19, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x53, 0x48, 0x52, 0x45,
	0x44, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e,
	0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x15, 0x0a, 0x11, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x4c, 0x4f, 0x54, 0x5f,
	0x44, 0x45, 0x41, 0x44, 0x10, 0x06, 0x32, 0xf5, 0x04, 0x0a, 0x06, 0x47, 0x65, 0x79, 0x73, 0x65,
	0x72, 0x12, 0x44, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x18,
	0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x60, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22,
	0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x33, 0x0a, 0x04, 0x50, 0x69, 0x6e,
	0x67, 0x12, 0x13, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x50, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x68, 0x61, 0x73, 0x68, 0x12, 0x21, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x68,
	0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x1d, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x3c, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x65,
	0x79, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57,
	0x0a, 0x10, 0x49, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x12, 0x1f, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x49, 0x73, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x49, 0x73, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x67, 0x65, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x3b,
	0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x70, 0x63,
	0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x79, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x74, 0x6f, 0x6e, 0x65,
	0x2d, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x2f, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_geyser_proto_rawDescOnce sync.Once
	file_geyser_proto_rawDescData = file_geyser_proto_rawDesc
)

func file_geyser_proto_rawDescGZIP() []byte {
	file_geyser_proto_rawDescOnce.Do(func() {
		file_geyser_proto_rawDescData = protoimpl.X.CompressGZIP(file_geyser_proto_rawDescData)
	})
	return file_geyser_proto_rawDescData
}

var file_geyser_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_geyser_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_geyser_proto_goTypes = []any{
	(CommitmentLevel)(0),                                 // 0: geyser.CommitmentLevel
	(SlotStatus)(0),                                      // 1: geyser.SlotStatus
	(*SubscribeRequest)(nil),                             // 2: geyser.SubscribeRequest
	(*SubscribeRequestFilterAccounts)(nil),               // 3: geyser.SubscribeRequestFilterAccounts
	(*SubscribeRequestFilterAccountsFilter)(nil),         // 4: geyser.SubscribeRequestFilterAccountsFilter
	(*SubscribeRequestFilterAccountsFilterMemcmp)(nil),   // 5: geyser.SubscribeRequestFilterAccountsFilterMemcmp
	(*SubscribeRequestFilterAccountsFilterLamports)(nil), // 6: geyser.SubscribeRequestFilterAccountsFilterLamports
	(*SubscribeRequestFilterSlots)(nil),                  // 7: geyser.SubscribeRequestFilterSlots
	(*SubscribeRequestFilterTransactions)(nil),           // 8: geyser.SubscribeRequestFilterTransactions
	(*SubscribeRequestFilterBlocks)(nil),                 // 9: geyser.SubscribeRequestFilterBlocks
	(*SubscribeRequestFilterBlocksMeta)(nil),             // 10: geyser.SubscribeRequestFilterBlocksMeta
	(*SubscribeRequestFilterEntry)(nil),                  // 11: geyser.SubscribeRequestFilterEntry
	(*SubscribeRequestAccountsDataSlice)(nil),            // 12: geyser.SubscribeRequestAccountsDataSlice
	(*SubscribeRequestPing)(nil),                         // 13: geyser.SubscribeRequestPing
	(*SubscribeUpdate)(nil),                              // 14: geyser.SubscribeUpdate
	(*SubscribeUpdateAccount)(nil),                       // 15: geyser.SubscribeUpdateAccount
	(*SubscribeUpdateAccountInfo)(nil),                   // 16: geyser.SubscribeUpdateAccountInfo
	(*SubscribeUpdateSlot)(nil),                          // 17: geyser.SubscribeUpdateSlot
	(*SubscribeUpdateTransaction)(nil),                   // 18: geyser.SubscribeUpdateTransaction
	(*SubscribeUpdateTransactionInfo)(nil),               // 19: geyser.SubscribeUpdateTransactionInfo
	(*SubscribeUpdateTransactionStatus)(nil),             // 20: geyser.SubscribeUpdateTransactionStatus
	(*SubscribeUpdateBlock)(nil),                         // 21: geyser.SubscribeUpdateBlock
	(*SubscribeUpdateBlockMeta)(nil),                     // 22: geyser.SubscribeUpdateBlockMeta
	(*SubscribeUpdateEntry)(nil),                         // 23: geyser.SubscribeUpdateEntry
	(*SubscribeUpdatePing)(nil),                          // 24: geyser.SubscribeUpdatePing
	(*SubscribeUpdatePong)(nil),                          // 25: geyser.SubscribeUpdatePong
	(*SubscribeReplayInfoRequest)(nil),                   // 26: geyser.SubscribeReplayInfoRequest
	(*SubscribeReplayInfoResponse)(nil),                  // 27: geyser.SubscribeReplayInfoResponse
	(*PingRequest)(nil),                                  // 28: geyser.PingRequest
	(*PongResponse)(nil),                                 // 29: geyser.PongResponse
	(*GetLatestBlockhashRequest)(nil),                    // 30: geyser.GetLatestBlockhashRequest
	(*GetLatestBlockhashResponse)(nil),                   // 31: geyser.GetLatestBlockhashResponse
	(*GetBlockHeightRequest)(nil),                        // 32: geyser.GetBlockHeightRequest
	(*GetBlockHeightResponse)(nil),                       // 33: geyser.GetBlockHeightResponse
	(*GetSlotRequest)(nil),                               // 34: geyser.GetSlotRequest
	(*GetSlotResponse)(nil),                              // 35: geyser.GetSlotResponse
	(*GetVersionRequest)(nil),                            // 36: geyser.GetVersionRequest
	(*GetVersionResponse)(nil),                           // 37: geyser.GetVersionResponse
	(*IsBlockhashValidRequest)(nil),                      // 38: geyser.IsBlockhashValidRequest
	(*IsBlockhashValidResponse)(nil),                     // 39: geyser.IsBlockhashValidResponse
	nil,                                                  // 40: geyser.SubscribeRequest.AccountsEntry
	nil,                                                  // 41: geyser.SubscribeRequest.SlotsEntry
	nil,                                                  // 42: geyser.SubscribeRequest.TransactionsEntry
	nil,                                                  // 43: geyser.SubscribeRequest.TransactionsStatusEntry
	nil,                                                  // 44: geyser.SubscribeRequest.BlocksEntry
	nil,                                                  // 45: geyser.SubscribeRequest.BlocksMetaEntry
	nil,                                                  // 46: geyser.SubscribeRequest.EntryEntry
	(*timestamppb.Timestamp)(nil),                        // 47: google.protobuf.Timestamp
	(*Transaction)(nil),                                  // 48: solana.storage.ConfirmedBlock.Transaction
	(*TransactionStatusMeta)(nil),                        // 49: solana.storage.ConfirmedBlock.TransactionStatusMeta
	(*TransactionError)(nil),                             // 50: solana.storage.ConfirmedBlock.TransactionError
	(*Rewards)(nil),                                      // 51: solana.storage.ConfirmedBlock.Rewards
	(*UnixTimestamp)(nil),                                // 52: solana.storage.ConfirmedBlock.UnixTimestamp
	(*BlockHeight)(nil),                                  // 53: solana.storage.ConfirmedBlock.BlockHeight
}
var file_geyser_proto_depIdxs = []int32{
	40, // 0: geyser.SubscribeRequest.accounts:type_name -> geyser.SubscribeRequest.AccountsEntry
	41, // 1: geyser.SubscribeRequest.slots:type_name -> geyser.SubscribeRequest.SlotsEntry
	42, // 2: geyser.SubscribeRequest.transactions:type_name -> geyser.SubscribeRequest.TransactionsEntry
	43, // 3: geyser.SubscribeRequest.transactions_status:type_name -> geyser.SubscribeRequest.TransactionsStatusEntry
	44, // 4: geyser.SubscribeRequest.blocks:type_name -> geyser.SubscribeRequest.BlocksEntry
	45, // 5: geyser.SubscribeRequest.blocks_meta:type_name -> geyser.SubscribeRequest.BlocksMetaEntry
	46, // 6: geyser.SubscribeRequest.entry:type_name -> geyser.SubscribeRequest.EntryEntry
	0,  // 7: geyser.SubscribeRequest.commitment:type_name -> geyser.CommitmentLevel
	12, // 8: geyser.SubscribeRequest.accounts_data_slice:type_name -> geyser.SubscribeRequestAccountsDataSlice
	13, // 9: geyser.SubscribeRequest.ping:type_name -> geyser.SubscribeRequestPing
	4,  // 10: geyser.SubscribeRequestFilterAccounts.filters:type_name -> geyser.SubscribeRequestFilterAccountsFilter
	5,  // 11: geyser.SubscribeRequestFilterAccountsFilter.memcmp:type_name -> geyser.SubscribeRequestFilterAccountsFilterMemcmp
	6,  // 12: geyser.SubscribeRequestFilterAccountsFilter.lamports:type_name -> geyser.SubscribeRequestFilterAccountsFilterLamports
	15, // 13: geyser.SubscribeUpdate.account:type_name -> geyser.SubscribeUpdateAccount
	17, // 14: geyser.SubscribeUpdate.slot:type_name -> geyser.SubscribeUpdateSlot
	18, // 15: geyser.SubscribeUpdate.transaction:type_name -> geyser.SubscribeUpdateTransaction
	20, // 16: geyser.SubscribeUpdate.transaction_status:type_name -> geyser.SubscribeUpdateTransactionStatus
	21, // 17: geyser.SubscribeUpdate.block:type_name -> geyser.SubscribeUpdateBlock
	24, // 18: geyser.SubscribeUpdate.ping:type_name -> geyser.SubscribeUpdatePing
	25, // 19: geyser.SubscribeUpdate.pong:type_name -> geyser.SubscribeUpdatePong
	22, // 20: geyser.SubscribeUpdate.block_meta:type_name -> geyser.SubscribeUpdateBlockMeta
	23, // 21: geyser.SubscribeUpdate.entry:type_name -> geyser.SubscribeUpdateEntry
	47, // 22: geyser.SubscribeUpdate.created_at:type_name -> google.protobuf.Timestamp
	16, // 23: geyser.SubscribeUpdateAccount.account:type_name -> geyser.SubscribeUpdateAccountInfo
	1,  // 24: geyser.SubscribeUpdateSlot.status:type_name -> geyser.SlotStatus
	19, // 25: geyser.SubscribeUpdateTransaction.transaction:type_name -> geyser.SubscribeUpdateTransactionInfo
	48, // 26: geyser.SubscribeUpdateTransactionInfo.transaction:type_name -> solana.storage.ConfirmedBlock.Transaction
	49, // 27: geyser.SubscribeUpdateTransactionInfo.meta:type_name -> solana.storage.ConfirmedBlock.TransactionStatusMeta
	50, // 28: geyser.SubscribeUpdateTransactionStatus.err:type_name -> solana.storage.ConfirmedBlock.TransactionError
	51, // 29: geyser.SubscribeUpdateBlock.rewards:type_name -> solana.storage.ConfirmedBlock.Rewards
	52, // 30: geyser.SubscribeUpdateBlock.block_time:type_name -> solana.storage.ConfirmedBlock.UnixTimestamp
	53, // 31: geyser.SubscribeUpdateBlock.block_height:type_name -> solana.storage.ConfirmedBlock.BlockHeight
	19, // 32: geyser.SubscribeUpdateBlock.transactions:type_name -> geyser.SubscribeUpdateTransactionInfo
	16, // 33: geyser.SubscribeUpdateBlock.accounts:type_name -> geyser.SubscribeUpdateAccountInfo
	23, // 34: geyser.SubscribeUpdateBlock.entries:type_name -> geyser.SubscribeUpdateEntry
	51, // 35: geyser.SubscribeUpdateBlockMeta.rewards:type_name -> solana.storage.ConfirmedBlock.Rewards
	52, // 36: geyser.SubscribeUpdateBlockMeta.block_time:type_name -> solana.storage.ConfirmedBlock.UnixTimestamp
	53, // 37: geyser.SubscribeUpdateBlockMeta.block_height:type_name -> solana.storage.ConfirmedBlock.BlockHeight
	0,  // 38: geyser.GetLatestBlockhashRequest.commitment:type_name -> geyser.CommitmentLevel
	0,  // 39: geyser.GetBlockHeightRequest.commitment:type_name -> geyser.CommitmentLevel
	0,  // 40: geyser.GetSlotRequest.commitment:type_name -> geyser.CommitmentLevel
	0,  // 41: geyser.IsBlockhashValidRequest.commitment:type_name -> geyser.CommitmentLevel
	3,  // 42: geyser.SubscribeRequest.AccountsEntry.value:type_name -> geyser.SubscribeRequestFilterAccounts
	7,  // 43: geyser.SubscribeRequest.SlotsEntry.value:type_name -> geyser.SubscribeRequestFilterSlots
	8,  // 44: geyser.SubscribeRequest.TransactionsEntry.value:type_name -> geyser.SubscribeRequestFilterTransactions
	8,  // 45: geyser.SubscribeRequest.TransactionsStatusEntry.value:type_name -> geyser.SubscribeRequestFilterTransactions
	9,  // 46: geyser.SubscribeRequest.BlocksEntry.value:type_name -> geyser.SubscribeRequestFilterBlocks
	10, // 47: geyser.SubscribeRequest.BlocksMetaEntry.value:type_name -> geyser.SubscribeRequestFilterBlocksMeta
	11, // 48: geyser.SubscribeRequest.EntryEntry.value:type_name -> geyser.SubscribeRequestFilterEntry
	2,  // 49: geyser.Geyser.Subscribe:input_type -> geyser.SubscribeRequest
	26, // 50: geyser.Geyser.SubscribeReplayInfo:input_type -> geyser.SubscribeReplayInfoRequest
	28, // 51: geyser.Geyser.Ping:input_type -> geyser.PingRequest
	30, // 52: geyser.Geyser.GetLatestBlockhash:input_type -> geyser.GetLatestBlockhashRequest
	32, // 53: geyser.Geyser.GetBlockHeight:input_type -> geyser.GetBlockHeightRequest
	34, // 54: geyser.Geyser.GetSlot:input_type -> geyser.GetSlotRequest
	38, // 55: geyser.Geyser.IsBlockhashValid:input_type -> geyser.IsBlockhashValidRequest
	36, // 56: geyser.Geyser.GetVersion:input_type -> geyser.GetVersionRequest
	14, // 57: geyser.Geyser.Subscribe:output_type -> geyser.SubscribeUpdate
	27, // 58: geyser.Geyser.SubscribeReplayInfo:output_type -> geyser.SubscribeReplayInfoResponse
	29, // 59: geyser.Geyser.Ping:output_type -> geyser.PongResponse
	31, // 60: geyser.Geyser.GetLatestBlockhash:output_type -> geyser.GetLatestBlockhashResponse
	33, // 61: geyser.Geyser.GetBlockHeight:output_type -> geyser.GetBlockHeightResponse
	35, // 62: geyser.Geyser.GetSlot:output_type -> geyser.GetSlotResponse
	39, // 63: geyser.Geyser.IsBlockhashValid:output_type -> geyser.IsBlockhashValidResponse
	37, // 64: geyser.Geyser.GetVersion:output_type -> geyser.GetVersionResponse
	57, // [57:65] is the sub-list for method output_type
	49, // [49:57] is the sub-list for method input_type
	49, // [49:49] is the sub-list for extension type_name
	49, // [49:49] is the sub-list for extension extendee
	0,  // [0:49] is the sub-list for field type_name
}

func init() { file_geyser_proto_init() }
func file_geyser_proto_init() {
	if File_geyser_proto != nil {
		return
	}
	file_solana_storage_proto_init()
	file_geyser_proto_msgTypes[0].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[1].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[2].OneofWrappers = []any{
		(*SubscribeRequestFilterAccountsFilter_Memcmp)(nil),
		(*SubscribeRequestFilterAccountsFilter_Datasize)(nil),
		(*SubscribeRequestFilterAccountsFilter_TokenAccountState)(nil),
		(*SubscribeRequestFilterAccountsFilter_Lamports)(nil),
	}
	file_geyser_proto_msgTypes[3].OneofWrappers = []any{
		(*SubscribeRequestFilterAccountsFilterMemcmp_Bytes)(nil),
		(*SubscribeRequestFilterAccountsFilterMemcmp_Base58)(nil),
		(*SubscribeRequestFilterAccountsFilterMemcmp_Base64)(nil),
	}
	file_geyser_proto_msgTypes[4].OneofWrappers = []any{
		(*SubscribeRequestFilterAccountsFilterLamports_Eq)(nil),
		(*SubscribeRequestFilterAccountsFilterLamports_Ne)(nil),
		(*SubscribeRequestFilterAccountsFilterLamports_Lt)(nil),
		(*SubscribeRequestFilterAccountsFilterLamports_Gt)(nil),
	}
	file_geyser_proto_msgTypes[5].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[6].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[7].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[12].OneofWrappers = []any{
		(*SubscribeUpdate_Account)(nil),
		(*SubscribeUpdate_Slot)(nil),
		(*SubscribeUpdate_Transaction)(nil),
		(*SubscribeUpdate_TransactionStatus)(nil),
		(*SubscribeUpdate_Block)(nil),
		(*SubscribeUpdate_Ping)(nil),
		(*SubscribeUpdate_Pong)(nil),
		(*SubscribeUpdate_BlockMeta)(nil),
		(*SubscribeUpdate_Entry)(nil),
	}
	file_geyser_proto_msgTypes[14].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[15].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[25].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[28].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[30].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[32].OneofWrappers = []any{}
	file_geyser_proto_msgTypes[36].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_geyser_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_geyser_proto_goTypes,
		DependencyIndexes: file_geyser_proto_depIdxs,
		EnumInfos:         file_geyser_proto_enumTypes,
		MessageInfos:      file_geyser_proto_msgTypes,
	}.Build()
	File_geyser_proto = out.File
	file_geyser_proto_rawDesc = nil
	file_geyser_proto_goTypes = nil
	file_geyser_proto_depIdxs = nil
}
