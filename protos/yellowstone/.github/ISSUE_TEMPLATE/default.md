---
name: Error
about: Issues with grpc plugin
title: "[Error] Describe the issue you are having."
labels: triage
assignees:

---

### Disclaimer

Please fill all the required data, if  not, the issue will not be considered and closed without any further review.
Before creating the issue, please do the following:

* Run the config check utility  `cargo run --bin config-check -- --config path_to_your_config/config.json`
* Ensure that solana version and yellowstone-grpc version are running with matching versions
* Ensure that solana and yellowstone-grpc are compiled with the same environment

### Describe your build and run environment 

If build and run environment are different, please add the details for both.

CPU: 
RAM:
OS:
Rust version:
Solana version:
Yellowstone grpc version:

### Config

Please paste your config file here and the result of the config-check:

### Describe your issue

Please describe your issue and append relevant logs.
When possible include steps to reproduce and code sample for the issue/repro.
