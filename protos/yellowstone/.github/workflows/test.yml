concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
  push:
    branches:
      - 'master'
      - 'v1.16'
      - 'v1.17'
      - 'v1.18'
      - 'v2.0'
      - 'v2.1'
      - 'v2.2'
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    strategy:
      matrix:
        os: [ubuntu-20.04, ubuntu-22.04]
    runs-on: ["${{ matrix.os }}"]
    steps:
      - name: Maximize build space
        uses: easimon/maximize-build-space@v10
        with:
          root-reserve-mb: 4096
          remove-dotnet: 'true'
          remove-android: 'true'
          remove-haskell: 'true'
          remove-codeql: 'true'

      - uses: actions/checkout@v4

      - uses: actions/cache@v4
        with:
          path: |
              ~/.cargo/bin/
              ~/.cargo/registry/index/
              ~/.cargo/registry/cache/
              ~/.cargo/git/db/
              ./target
          key: v0001-${{ matrix.os }}-rust-${{ hashFiles('rust-toolchain.toml') }}-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            v0001-${{ matrix.os }}-rust-${{ hashFiles('rust-toolchain.toml') }}

      - name: Set rust version
        run: |
          RUST_VERSION="$(grep -oP 'channel = "\K\d\.\d+\.\d+(?=")' rust-toolchain.toml)"
          echo "RUST_STABLE=$RUST_VERSION" | tee -a $GITHUB_ENV

      - name: Set env vars
        run: |
          source ci/env.sh
          echo "GEYSER_PLUGIN_NAME=$plugin_name" | tee -a $GITHUB_ENV
          echo "GEYSER_PLUGIN_LIB=lib${plugin_lib_name}" | tee -a $GITHUB_ENV

      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: nightly
          components: rustfmt

      - uses: dtolnay/rust-toolchain@stable
        with:
          targets: wasm32-unknown-unknown
          toolchain: ${{ env.RUST_STABLE }}

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libsasl2-dev protobuf-compiler

      - name: cargo tree
        run: |
          cargo tree
          git diff Cargo.lock
          git checkout Cargo.lock
          cargo tree --frozen

      - name: cargo tree wasm
        run: |
          cd yellowstone-grpc-client-nodejs/solana-encoding-wasm
          cargo tree
          git diff Cargo.lock
          git checkout Cargo.lock
          cargo tree --frozen

      - name: cargo fmt
        run: cargo +nightly fmt --all -- --check

      - name: cargo fmt wasm
        run: |
          cd yellowstone-grpc-client-nodejs/solana-encoding-wasm
          cargo +nightly fmt --all -- --check

      - name: cargo deny check advisories
        uses: EmbarkStudios/cargo-deny-action@v2.0.1
        with:
          command: check advisories
          rust-version: ${{ env.RUST_STABLE }}

      - name: cargo deny check advisories wasm
        uses: EmbarkStudios/cargo-deny-action@v2.0.1
        with:
          command: check advisories
          rust-version: ${{ env.RUST_STABLE }}
          manifest-path: ./yellowstone-grpc-client-nodejs/solana-encoding-wasm/Cargo.toml

      - name: cargo clippy
        run: cargo clippy --workspace --all-targets
      - name: cargo clippy wasm
        run: |
          cd yellowstone-grpc-client-nodejs/solana-encoding-wasm
          cargo clippy --target wasm32-unknown-unknown --all-targets

      - name: check features in `client`
        run: cargo check -p yellowstone-grpc-client --all-targets
      - name: check features in `client-simple`
        run: cargo check -p yellowstone-grpc-client-simple --all-targets
      - name: check features in `geyser`
        run: cargo check -p yellowstone-grpc-geyser --all-targets
      - name: check features in `proto`
        run: cargo check -p yellowstone-grpc-proto --all-targets
      - name: check features in `proto`
        run: cargo check -p yellowstone-grpc-proto --all-targets --all-features

      - name: cargo test
        run: cargo test --all-features

      - name: Build
        run: ./ci/cargo-build-test.sh
